<template>
  <div class="px-16">
    <div class="flex justify-end mb-8">
      <el-button type="primary" @click="handleAdd">
        添加报告
      </el-button>
    </div>
    <el-table :data="tableData">
      <el-table-column label="序号" type="index" width="80" />

      <el-table-column label="报告标题" min-width="150" prop="title" show-overflow-tooltip />

      <el-table-column label="报告简介" min-width="200" prop="description" show-overflow-tooltip />

      <el-table-column align="center" label="报告封面图" prop="coverImage" width="120">
        <template #default="scope">
          <el-image
            class="h-80 w-80"
            :preview-src-list="[scope.row.coverImage]"
            :src="scope.row.coverImage"
          />
        </template>
      </el-table-column>

      <el-table-column label="报告时间" prop="reportTime" width="160">
        <template #default="scope">
          {{ scope.row.reportTime }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="报告主图" prop="mainImage" width="120">
        <template #default="scope">
          <el-image
            class="h-80 w-80"
            :preview-src-list="[scope.row.mainImage]"
            :src="scope.row.mainImage"
          />
        </template>
      </el-table-column>

      <el-table-column label="所属行业" prop="industry_code" width="120">
        <template #default="scope">
          {{ findNodeById(industryOptions, 'mp_industry_code', scope.row.industry_code)?.industry_name || '【异常数据】' }}
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="status" width="120">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-text="scope.row.status === 1 ? '启用' : '停用'"
            :active-value="1"
            :inactive-value="0"
          />
        </template>
      </el-table-column>

      <el-table-column label="操作时间" prop="operateTime" width="160">
        <template #default="scope">
          {{ scope.row.operateTime }}
        </template>
      </el-table-column>

      <el-table-column label="操作人" prop="operator" width="120" />

      <el-table-column label="PDF文件" min-width="150" prop="pdfUrl" show-overflow-tooltip>
        <template #default="scope">
          <el-link :href="scope.row.pdfUrl" target="_blank" type="primary">
            查看PDF
          </el-link>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100">
        <template #default="scope">
          <el-button link type="primary" @click="handleEdit(scope.row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <AddModal
      ref="modalRef"
      @success="handleSuccess"
    />

    <EditModal
      ref="editModalRef"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import type { ReportItem } from './hooks/useData'
import { findNodeById } from '@/utils/data'
import AddModal from './components/AddModal.vue'
import EditModal from './components/EditModal.vue'

const { industryOptions } = storeToRefs(useAccountStore())

const tableData = ref<ReportItem[]>([
  {
    title: '2024年人工智能产业发展研究报告',
    description: '本报告深入分析了2024年人工智能行业的发展现状、技术趋势、市场规模及未来展望',
    coverImage: 'https://example.oss-cn-beijing.aliyuncs.com/reports/cover/ai-2024.jpg',
    mainImage: 'https://example.oss-cn-beijing.aliyuncs.com/reports/main/ai-2024.jpg',
    pdfUrl: 'https://example.oss-cn-beijing.aliyuncs.com/reports/pdf/ai-2024.pdf',
    reportTime: '2024-03-15 10:00:00',
    industry_code: '3',
    status: 1,
    operateTime: '2024-03-14 16:30:00',
    operator: '张明',
  },
  {
    title: '2024年新能源汽车行业分析报告',
    description: '深度解析新能源汽车产业链发展情况，包含市场规模、技术创新、政策影响等多个维度',
    coverImage: 'https://example.oss-cn-beijing.aliyuncs.com/reports/cover/ev-2024.jpg',
    mainImage: 'https://example.oss-cn-beijing.aliyuncs.com/reports/main/ev-2024.jpg',
    pdfUrl: 'https://example.oss-cn-beijing.aliyuncs.com/reports/pdf/ev-2024.pdf',
    reportTime: '2024-03-10 14:00:00',
    industry_code: '1',
    status: 0,
    operateTime: '2024-03-09 15:20:00',
    operator: '李华',
  },
])

const modalRef = ref()
const editModalRef = ref()

function handleAdd() {
  modalRef.value?.show()
}

function handleEdit(row: ReportItem) {
  editModalRef.value?.show(row)
}

function handleSuccess() {
  // TODO: 调用获取列表接口
  ElMessage.success('操作成功')
}
</script>

<style scoped>

</style>
