<template>
  <el-config-provider
    :locale="locale === 'zh-CN' ? locales.zh : null"
    size="large"
  >
    <Component :is="layout">
      <RouterView v-slot="{ Component: RouteComponent }">
        <Transition mode="out-in" name="fade">
          <Component :is="RouteComponent" />
        </Transition>
      </RouterView>
    </Component>
  </el-config-provider>
</template>

<script setup lang="ts">
import en from 'element-plus/es/locale/lang/en'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import CommonLayout from './views/layout/CommonLayout.vue'
import EmptyLayout from './views/layout/EmptyLayout.vue'

const route = useRoute()

const layout = computed(() => {
  if (!route.name) return EmptyLayout
  if (route.name === 'login') return EmptyLayout
  return CommonLayout
})

const locales = {
  zh: zhCn,
  en,
}
const { locale } = useI18n()
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s cubic-bezier(0.55, 0, 0.1, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
