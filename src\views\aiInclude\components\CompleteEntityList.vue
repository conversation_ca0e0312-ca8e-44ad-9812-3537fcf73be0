<template>
  <div class="flex flex-col gap-16 h-full">
    <Handsontable
      ref="hotTableRef"
      :columns="columns"
      :data="tableData"
      :fixed-columns-left="3"
      :post-transform="postTransform"
      :transform="transform"
    />
    <el-button class="self-end" type="primary" @click="onConfirm">
      保存
    </el-button>
  </div>
</template>

<script setup lang="ts">
import type { HandsontableColumn } from '@/components/Handsontable/Handsontable.vue'
import type { WebInfo } from '../hooks/type'
import { useData } from '../hooks/useData'
import { useFilter } from '../hooks/useFilter'
import { ElMessage } from 'element-plus'

const emit = defineEmits<{
  (e: 'saveSuccess'): void
}>()

const {
  getAiWebInfo,
  updateAiWebInfo,
} = useData()
const {
  taskId,
  allAiIndustryList,
} = useFilter()

const hotTableRef = shallowRef()

const collectStatusOptions = [
  {
    label: '正常周期采集',
    value: 0,
  },
  {
    label: '停止采集',
    value: 1,
  },
  {
    label: '单独需求采集',
    value: 2,
  },
]

const columns = computed(() => {
  const columns: HandsontableColumn[] = [
    {
      name: 'web_id',
      label: 'WEB ID',
      editor: false,
    },
    {
      name: 'name',
      label: 'WEB名称',
    },
    {
      name: 'logo_url',
      label: '图标URL',
      renderer(instance, TD, row, col, prop, value) {
        const html = `<img src="${value}" style="width: 56px; height: 56px;">`
        TD.innerHTML = html
      },
    },
    {
      name: 'domain',
      label: '网址',
    },
    {
      name: 'company_fname',
      label: '公司全称',
    },
    {
      name: 'company_sname',
      label: '公司简称',
    },
    {
      name: 'group_company',
      label: '所属集团',
    },
    {
      name: 'region',
      label: '地区',
      editor: 'select',
      selectOptions: ['中国', '海外'],
    },
    {
      name: 'is_export',
      label: '是否出海',
      editor: 'select',
      selectOptions: ['是', '否'],
    },
    {
      name: 'industry_l1_code_ai_label',
      label: 'AI行业打标结果',
      editor: false,
      renderer(instance, TD, row, col, prop, value) {
        if (!value) {
          return
        }
        const codeList = value.split(',')
        const codeListStr = codeList.map((code: string) => {
          const codeItem = allAiIndustryList.value.find(item => item.industry_code === Number(code))
          return codeItem?.industry_name
        }).join('<br/>')
        TD.innerHTML = codeListStr
      },
    },
    {
      name: 'is_ai',
      label: 'AI原生',
      editor: 'select',
      selectOptions: ['是', '否'],
    },
    {
      name: 'industry_l1_code',
      label: '一级行业 ID',
      type: 'autocomplete',
      strict: true,
      source: allAiIndustryList.value.map(item => item.industry_name),
    },
    {
      name: 'release_time',
      label: '上线时间',
      type: 'date',
      dateFormat: 'YYYY-MM',
      correctFormat: true,
      allowEmpty: false,
    },
    {
      name: 'collect_status',
      label: '采集状态',
      editor: false,
    },
    {
      name: 'introduction',
      label: '描述',
    },
    {
      name: 'remark',
      label: '备注',
    },
    {
      name: 'scale',
      label: '量级',
      editor: false,
    },
    {
      name: 'is_scale_standard',
      label: '量级是否达标',
      editor: false,
    },
    {
      name: 'discover_time',
      label: '首次新增时间',
      editor: false,
    },
    {
      name: 'collect_time',
      label: '首次收录时间',
      editor: false,
    },
  ]
  return columns
})

function transform(list: WebInfo[]) {
  return list.map((data) => {
    const res: any = {
      ...data,
    }
    res.is_ai = data.is_ai ? '是' : '否'
    res.is_export = data.is_export ? '是' : '否'
    res.is_scale_standard = data.is_scale_standard ? '是' : '否'
    res.region = data.region === 'china' ? '中国' : '海外'
    res.collect_status = collectStatusOptions.find(item => item.value === data.collect_status)?.label
    if (!res.industry_l1_code && data.industry_l1_code_ai_label) {
      const firstCode = data.industry_l1_code_ai_label.split(',')[0]
      res.industry_l1_code = allAiIndustryList.value
        .find(item => item.industry_code.toString() === firstCode)?.industry_name
    }
    else if (res.industry_l1_code) {
      res.industry_l1_code = allAiIndustryList.value
        .find(item => item.industry_code === data.industry_l1_code)?.industry_name
    }
    return res
  })
}

function postTransform(list: WebInfo[]) {
  return list.map((data: any) => {
    const res: any = {
      ...data,
    }
    res.is_ai = data.is_ai === '是'
    res.is_export = data.is_export === '是'
    res.is_scale_standard = data.is_scale_standard === '是'
    res.region = data.region === '中国' ? 'china' : 'overseas'
    res.industry_l1_code = allAiIndustryList.value
      .find(item => item.industry_name === data.industry_l1_code)?.industry_code
    res.collect_status = collectStatusOptions.find(item => item.label === data.collect_status)?.value

    return res
  })
}

const tableData = ref<WebInfo[]>([])

async function loadData() {
  const { data } = await getAiWebInfo(taskId.value)
  tableData.value = data
}

loadData()

async function onConfirm() {
  const changedData = hotTableRef.value?.getData()
  const paramData = changedData
  const { error_code, error_msg } = await updateAiWebInfo(taskId.value, paramData)
  if (error_code === 0) {
    ElMessage.success('保存成功')
    emit('saveSuccess')
  }
  else {
    ElMessage.error(error_msg)
  }
}
</script>

<style scoped lang="scss">

</style>
