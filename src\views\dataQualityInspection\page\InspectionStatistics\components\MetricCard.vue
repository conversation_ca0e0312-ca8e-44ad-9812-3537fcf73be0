<template>
  <div class="statistic-card">
    <el-statistic v-if="status === 'success'" :value="value">
      <template #title>
        <div style="display: inline-flex; align-items: center">
          {{ title }}
        </div>
      </template>
    </el-statistic>
    <el-statistic v-else :formatter="(value:number) => value === 0 ? '' : value" :value="0">
      <template #title>
        <div style="display: inline-flex; align-items: center">
          {{ title }}
        </div>
      </template>
      <template #suffix>
        {{
          status === 'in_progress' ? '正在巡检'
          : status === 'loading' ? '加载中' : '巡检失败'
        }}
      </template>
    </el-statistic>
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  title: string
  value: number
  status: 'success' | 'failure' | 'in_progress' | 'loading'
}>()
</script>

<style scoped>
.el-statistic {
  --el-statistic-content-font-size: 28px;
}

.statistic-card {
  @apply h-full p-20 rounded bg-white dark:bg-black w-200;
}
</style>
