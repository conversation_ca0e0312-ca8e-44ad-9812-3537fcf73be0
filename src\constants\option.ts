export const ACCOUNT_TYPE_OPTIONS = [
  { value: 'internal', label: '内部账号' },
  { value: 'external', label: '外部账号' },
]

export const LOGIN_TYPE_OPTIONS = [
  { label: '微信扫码', value: 0 },
  { label: '账号密码', value: 1 },
]

export const NUMBER_CHINESE = ['零', '一', '二', '三']

export const MK_RISK_DEFAULT_BRAND_OPTIONS = [
  { label: '五粮液', value: **************** },
  { label: '五粮液1618', value: **************** },
  { label: '第八代五粮液', value: **************** },
  { label: '五粮春', value: **************** },
  { label: '赖茅', value: **************** },
  { label: '贵州茅台', value: **************** },
  { label: '泸州老窖', value: **************** },
  { label: '汾酒', value: **************** },
  { label: '古井贡', value: **************** },
  { label: '郎酒', value: **************** },
  { label: '剑南春', value: **************** },
  { label: '习酒', value: **************** },
  { label: '水井坊', value: 1030000000008112 },
  { label: '洋河', value: 1030000000008367 },
  { label: '牛栏山', value: 1030000000008923 },
  { label: '豆包', value: 1030000000044736 },
  { label: '通义', value: 1030000000044735 },
  { label: '文心一言', value: 1030000000044734 },
  { label: 'Kimi', value: 1030000000044733 },
  { label: '天工AI', value: 1030000000044732 },
  { label: '腾讯元宝', value: 1030000000044731 },
  { label: '讯飞星火', value: 1030000000044730 },
  { label: '智谱清言', value: 1030000000044729 },
  { label: 'OLAY', value: 1030000000000412 },
  { label: 'SK-II', value: 1030000000000480 },
  { label: 'babycare', value: 1030000000044695 },
  { label: '全棉时代', value: 1030000000004007 },
  { label: '好奇', value: 1030000000005498 },
  { label: '帮宝适', value: 1030000000027838 },
  { label: '佳洁士', value: 1030000000003800 },
  { label: '中华', value: 1030000000003117 },
  { label: '沙宣', value: 1030000000008247 },
  { label: '海飞丝', value: 1030000000008525 },
  { label: '飘柔', value: 1030000000012612 },
  { label: '潘婷', value: 1030000000008639 },
  { label: '舒肤佳', value: 1030000000010512 },
  { label: '多芬', value: 1030000000005224 },
  { label: '碧浪', value: 1030000000035617 },
  { label: '当妮', value: 1030000000006444 },
  { label: '汰渍', value: 1030000000032409 },
  { label: '奥妙', value: 1030000000025696 },
  { label: '护舒宝', value: 1030000000006854 },
  { label: '丹碧丝', value: 1030000000003179 },
  { label: '博朗', value: 1030000000004528 },
  { label: '吉列', value: 1030000000004784 },
  { label: '伊维安', value: 1030000000044728 },
  { label: '平安人寿', value: 1030000000044701 },
  { label: '平安保险', value: 1030000000044700 },
  { label: '平安产险', value: 1030000000044702 },
  { label: '平安健康险', value: 1030000000044703 },
  { label: '平安养老险', value: 1030000000044704 },
  { label: '中国人寿', value: 1030000000044698 },
  { label: '太保人寿', value: 1030000000044712 },
  { label: '新华人寿', value: 1030000000044711 },
  { label: '泰康人寿', value: 1030000000044708 },
  { label: '瑞众人寿', value: 1030000000044713 },
  { label: '众安保险', value: 1030000000044705 },
  { label: '蚂蚁保', value: 1030000000044707 },
  { label: '腾讯微保', value: 1030000000044706 },
  { label: '慧择保险', value: 1030000000044714 },
  { label: '瑞泰人寿', value: 1030000000044715 },
  { label: '中国人保', value: 1030000000044696 },
  { label: '人保寿险', value: 1030000000044716 },
  { label: '友邦人寿', value: 1030000000044717 },
  { label: '太平人寿', value: 1030000000044718 },
  { label: '富德生命人寿', value: 1030000000044719 },
  { label: '中华联合保险', value: 1030000000044720 },
  { label: '京东保险', value: 1030000000044721 },
  { label: '太平洋保险', value: 1030000000044697 },
  { label: '友邦保险', value: 1030000000044709 },
  { label: '保诚', value: 1030000000044710 },
  { label: '平安银行', value: 1030000000044694 },
  { label: '农夫山泉', value: 1030000000021997 },
  { label: '百岁山', value: 1030000000044680 },
  { label: '娃哈哈', value: 1030000000005626 },
  { label: '今麦郎凉白开', value: 1030000000044687 },
  { label: '怡宝', value: 1030000000006655 },
  { label: '东方树叶', value: 1030000000019482 },
  { label: '三得利', value: 1030000000019214 },
  { label: '维他', value: 1030000000009980 },
  { label: '美汁源', value: 1030000000037270 },
  { label: '统一鲜橙多', value: 1030000000044688 },
  { label: '17.5°', value: 1030000000044689 },
  { label: '山楂树下', value: 1030000000044690 },
  { label: '可口可乐', value: 1030000000004735 },
  { label: '百事可乐', value: 1030000000044691 },
  { label: '元气森林', value: 1030000000021660 },
  { label: '佳得乐', value: 1030000000021289 },
  { label: '红牛', value: 1030000000036593 },
  { label: '脉动', value: 1030000000037792 },
  { label: '外星人电解质水', value: 1030000000044692 },
  { label: '东鹏饮料', value: 1030000000044693 },
  { label: '雀巢咖啡', value: 1030000000012278 },
  { label: '星巴克', value: 1030000000007232 },
  { label: '三顿半', value: 1030000000019323 },
  { label: '隅田川', value: 1030000000012269 },
  { label: '白象', value: 1030000000035004 },
  { label: '康师傅', value: 1030000000028029 },
  { label: '统一', value: 1030000000036780 },
  { label: '今麦郎', value: 1030000000020666 },
  { label: '汤达人', value: 1030000000032401 },
  { label: '空刻', value: 1030000000044465 },
  { label: '自嗨锅', value: 1030000000037833 },
  { label: '豫竹', value: 1030000000040205 },
  { label: '阿宽食品', value: 1030000000044870 },
  { label: '五谷道场', value: 1030000000020401 },
  { label: '乐事', value: 1030000000003234 },
  { label: '三只松鼠', value: 1030000000019178 },
  { label: '良品铺子', value: 1030000000038028 },
  { label: '比比赞', value: 1030000000032055 },
  { label: '百草味', value: 1030000000035173 },
  { label: '来伊份', value: 1030000000030744 },
  { label: '洽洽食品', value: 1030000000044869 },
  { label: '徐福记', value: 1030000000028283 },
  { label: '华味亨', value: 1030000000022801 },
  { label: '舌里', value: 1030000000037934 },
  { label: '小鹏汽车', value: 1030000000044072 },
  { label: '理想汽车', value: 1030000000009115 },
  { label: '小米汽车', value: 1030000000044148 },
  { label: '长城汽车', value: 1030000000044682 },
  { label: '奔驰', value: 1030000000044666 },
  { label: 'smart', value: 1030000000044673 },
  { label: '沃尔玛', value: 1030000000044573 },
  { label: '山姆', value: 1030000000044572 },
  { label: '胖东来', value: 1030000000037750 },
  { label: '戴森', value: 1030000000006828 },
  { label: '徕芬', value: 1030000000006474 },
  { label: '美的', value: 1030000000010230 },
  { label: '九阳', value: 1030000000003360 },
  { label: '立白', value: 1030000000009708 },
  { label: '蓝月亮', value: 1030000000011171 },
  { label: '安踏', value: 1030000000026503 },
  { label: 'FILA', value: 1030000000044699 },
  { label: '耐克', value: 1030000000010340 },
  { label: '茶百道', value: 1030000000038788 },
  { label: '霸王茶姬', value: 1030000000042684 },
  { label: '蔡司', value: 1030000000011201 },
  { label: '依视路', value: 1030000000021390 },
  { label: '万新光学', value: 1030000000044722 },
  { label: '豪雅', value: 1030000000044723 },
  { label: '善存', value: 1030000000024439 },
  { label: '斯维诗', value: 1030000000044370 },
  { label: '养生堂', value: 1030000000004071 },
  { label: '汤臣倍健', value: 1030000000008218 },
  { label: '钙尔奇', value: 1030000000041747 },
  { label: '21金维他', value: 1030000000012967 },
  { label: '黄金搭档', value: 1030000000043762 },
  { label: '迪巧', value: 1030000000040955 },
  { label: '麦谷村', value: 1030000000044726 },
  { label: '幸棉', value: 1030000000044725 },
  { label: '科净威', value: 1030000000044459 },
  { label: '喜之郎', value: 1030000000004995 },
  { label: '蜡笔小新', value: 1030000000011312 },
  { label: '亲亲', value: 1030000000020557 },
  { label: '苹果', value: 1030000000010872 },
  { label: '华为', value: 1030000000004393 },
  { label: 'vivo', value: 1030000000002739 },
  { label: 'OPPO', value: 1030000000000406 },
  { label: '红米', value: 1030000000009904 },
  { label: 'iQOO', value: 1030000000001505 },
  { label: '荣耀', value: 1030000000010939 },
  { label: '一加', value: 1030000000002860 },
  { label: '真我', value: 1030000000044455 },
  { label: '三星', value: 1030000000002974 },
  { label: '小米', value: 1030000000006054 },
  { label: '联想', value: 1030000000010354 },
  { label: '华硕', value: 1030000000004445 },
  { label: '惠普', value: 1030000000006769 },
  { label: '戴尔', value: 1030000000006825 },
  { label: '英特尔', value: 1030000000010862 },
  { label: 'Thinkpad', value: 1030000000044727 },
  { label: '兰蔻', value: 1030000000004051 },
  { label: '雅诗兰黛', value: 1030000000012336 },
  { label: '欧莱雅', value: 1030000000008015 },
  { label: '薇诺娜', value: 1030000000011246 },
  { label: '海蓝之谜', value: 1030000000008515 },
  { label: '珀莱雅', value: 1030000000009069 },
  { label: '娇韵诗', value: 1030000000005640 },
  { label: '羽西', value: 1030000000010304 },
  { label: '华熙生物', value: 1030000000004437 },
  { label: '馥蕾诗', value: 1030000000012692 },
  { label: '蜜丝婷', value: 1030000000011288 },
  { label: '逐本', value: 1030000000011828 },
  { label: '花西子', value: 1030000000010789 },
  { label: '简爱', value: 1030000000009754 },
  { label: '卡士', value: 1030000000004570 },
  { label: '简醇', value: 1030000000036238 },
  { label: '迈巴赫', value: 1030000000040866 },
  { label: '泡泡玛特', value: 1030000000008309 },
  { label: '名创优品MINISO', value: 1030000000044741 },
  { label: '52TOYS', value: 1030000000044742 },
  { label: '寻找独角兽', value: 1030000000044743 },
  { label: '十二栋文化', value: 1030000000044744 },
  { label: '萌奇', value: 1030000000011079 },
  { label: 'TOP TOY', value: 1030000000044745 },
  { label: '酷乐潮玩', value: 1030000000041263 },
  { label: '19八3', value: 1030000000044746 },
  { label: '九木杂物社', value: 1030000000044747 },
  { label: 'IP 小站', value: 1030000000044748 },
  { label: 'TNTSPACE', value: 1030000000044749 },
  { label: 'Sonny Angel', value: 1030000000044750 },
  { label: 'tokidoki', value: 1030000000002612 },
  { label: '361°', value: 1030000000012974 },
  { label: '李宁', value: 1030000000007493 },
  { label: '斯凯奇', value: 1030000000029552 },
  { label: '特步', value: 1030000000034223 },
  { label: '阿迪达斯', value: 1030000000012253 },
  { label: '乔丹', value: 1030000000044255 },
  { label: '鸿星尔克', value: 1030000000043539 },
  { label: 'NEW BALANCE', value: 1030000000044196 },
  { label: '亚瑟士', value: 1030000000020445 },
  { label: '蒙牛', value: 1030000000039298 },
  { label: '金典', value: 1030000000011984 },
  { label: '君乐宝', value: 1030000000023944 },
  { label: '伊利', value: 1030000000020760 },
  { label: '安慕希', value: 1030000000026427 },
  { label: '认养一头牛', value: 1030000000039930 },
  { label: '特仑苏', value: 1030000000034214 },
  { label: '光明乳业', value: 1030000000044751 },
  { label: '德亚', value: 1030000000028391 },
  { label: '燕塘', value: 1030000000033801 },
  { label: '肯德基', value: 1030000000044740 },
  { label: '麦当劳', value: 1030000000044739 },
  { label: '汉堡王', value: 1030000000008182 },
  { label: '德克士', value: 1030000000006525 },
  { label: '必胜客', value: 1030000000028564 },
  { label: 'GoPro', value: 1030000000000223 },
  { label: '大疆DJI', value: 1030000000044752 },
  { label: 'Insta360影石', value: 1030000000044753 },
  { label: '索尼', value: 1030000000009855 },
  { label: '萤石', value: 1030000000011087 },
  { label: 'SJCAM', value: 1030000000000479 },
  { label: '山狗', value: 1030000000006156 },
  { label: '小蚁', value: 1030000000006066 },
  { label: '松下相机', value: 1030000000044754 },
  { label: 'KOMERY', value: 1030000000000307 },
  { label: '富士', value: 1030000000005944 },
  { label: '杰伟世(JVC)', value: 1030000000044755 },
  { label: '无印良品', value: 1030000000007128 },
  { label: '三福', value: 1030000000044756 },
  { label: 'NOME', value: 1030000000000386 },
  { label: 'Minigood', value: 1030000000044757 },
  { label: 'KKV', value: 1030000000044758 },
  { label: '屈臣氏', value: 1030000000006142 },
  { label: '小爱同学', value: 1030000000044759 },
  { label: '华为小艺', value: 1030000000044760 },
  { label: 'Jovi', value: 1030000000044761 },
  { label: '蓝心小V', value: 1030000000044762 },
  { label: 'OPPO小布', value: 1030000000044763 },
  { label: '苹果 Siri', value: 1030000000044764 },
  { label: '三星Bixby', value: 1030000000044765 },
  { label: '荣耀YOYO', value: 1030000000044766 },
  { label: '魅族Flyme', value: 1030000000044767 },
  { label: '芒果TV', value: 1030000000044768 },
  { label: '爱奇艺', value: 1030000000044769 },
  { label: '腾讯视频', value: 1030000000044770 },
  { label: '优酷视频', value: 1030000000044771 },
  { label: '抖音', value: 1030000000044772 },
  { label: '哔哩哔哩', value: 1030000000044773 },
  { label: '快手', value: 1030000000044774 },
  { label: '剪映', value: 1030000000044775 },
  { label: '美图秀秀', value: 1030000000044778 },
  { label: '醒图', value: 1030000000044779 },
  { label: '美颜相机', value: 1030000000044780 },
  { label: '轻颜', value: 1030000000044781 },
  { label: 'Wink', value: 1030000000044782 },
  { label: '邓禄普', value: 1030000000041141 },
  { label: '米其林', value: 1030000000036266 },
  { label: '马牌', value: 1030000000012706 },
  { label: '普利司通', value: 1030000000030209 },
  { label: '佳通轮胎', value: 1030000000044776 },
  { label: '固特异', value: 1030000000005061 },
  { label: '倍耐力', value: 1030000000021550 },
  { label: '韩泰', value: 1030000000044777 },
  { label: '奔跑吧 第八季', value: 1030000000044783 },
  { label: '极限挑战 第十季', value: 1030000000044784 },
  { label: '大侦探 第九季', value: 1030000000044785 },
  { label: '歌手2024', value: 1030000000044786 },
  { label: '新说唱2024', value: 1030000000044787 },
  { label: '乘风2024', value: 1030000000044788 },
  { label: '有歌2024', value: 1030000000044789 },
  { label: '心动的信号 第七季', value: 1030000000044790 },
  { label: '披荆斩棘 第四季', value: 1030000000044791 },
  { label: '花儿与少年 第六季', value: 1030000000044792 },
  { label: '再见爱人 第四季', value: 1030000000044793 },
  { label: '桃花坞 第四季', value: 1030000000044794 },
  { label: '天赐的声音 第五季', value: 1030000000044795 },
  { label: '我们的歌 第六季', value: 1030000000044796 },
  { label: '密室大逃脱 第六季', value: 1030000000044797 },
  { label: '喜剧之王单口季', value: 1030000000044798 },
  { label: '令人心动的offer 第六季', value: 1030000000044799 },
  { label: '中餐厅 第八季', value: 1030000000044800 },
  { label: '你好，星期六', value: 1030000000044801 },
  { label: '城市捉迷藏', value: 1030000000044802 },
  { label: '狐妖小红娘·月红篇', value: 1030000000044803 },
  { label: '暗夜与黎明', value: 1030000000044804 },
  { label: '珠帘玉幕', value: 1030000000044805 },
  { label: '大梦归离', value: 1030000000044806 },
  { label: '锦绣安宁', value: 1030000000044807 },
  { label: '要久久爱', value: 1030000000044808 },
  { label: '庆余年2', value: 1030000000044809 },
  { label: '与凤行', value: 1030000000044810 },
  { label: '长相思第二季', value: 1030000000044811 },
  { label: '小巷人家', value: 1030000000044812 },
  { label: '永夜星河', value: 1030000000044813 },
  { label: '在暴雪时分', value: 1030000000044814 },
  { label: '玫瑰的故事', value: 1030000000044815 },
  { label: '墨雨云间', value: 1030000000044816 },
  { label: '花间令', value: 1030000000044817 },
  { label: '我的阿勒泰', value: 1030000000044818 },
  { label: '哈尔滨1944', value: 1030000000044819 },
  { label: '春花焰', value: 1030000000044820 },
  { label: '七夜雪', value: 1030000000044821 },
  { label: '破茧2', value: 1030000000044822 },
  { label: '执笔', value: 1030000000044823 },
  { label: '至尊丐婿', value: 1030000000044824 },
  { label: '爱在黎明前降落', value: 1030000000044825 },
  { label: '我在八零年代当后妈', value: 1030000000044826 },
  { label: '难寻', value: 1030000000044827 },
  { label: '裴总每天都想父凭子贵', value: 1030000000044828 },
  { label: '病娇反派攻略计划', value: 1030000000044829 },
  { label: '司令宠妾灭妻，我转身出府嫁少帅', value: 1030000000044830 },
  { label: '引她入室', value: 1030000000044831 },
  { label: '无极归来', value: 1030000000044832 },
  { label: '遥不可及的爱', value: 1030000000044833 },
  { label: '授她以柄', value: 1030000000044834 },
  { label: '游子归家', value: 1030000000044835 },
  { label: '宫墙雪', value: 1030000000044836 },
  { label: '热辣滚烫的婚姻', value: 1030000000044837 },
  { label: '厉总，你找错夫人了', value: 1030000000044838 },
  { label: '乘风破浪的婚姻', value: 1030000000044839 },
  { label: '消失的厨神', value: 1030000000044840 },
  { label: '撒娇大叔最好命', value: 1030000000044841 },
  { label: '大王别慌张', value: 1030000000044842 },
  { label: '第二十条', value: 1030000000044843 },
  { label: '乔妍的心事', value: 1030000000044844 },
  { label: '草木人间', value: 1030000000044845 },
  { label: '浴火之路', value: 1030000000044846 },
  { label: '热辣滚烫', value: 1030000000044847 },
  { label: '被我弄丢的你', value: 1030000000044848 },
  { label: '没有一顿火锅解决不了的事', value: 1030000000044849 },
  { label: '负负得正', value: 1030000000044850 },
  { label: '云边有个小卖部', value: 1030000000044851 },
  { label: '欢迎来到我身边', value: 1030000000044852 },
  { label: '维和防暴队', value: 1030000000044853 },
  { label: '传说', value: 1030000000044854 },
  { label: '扫黑·决不放弃', value: 1030000000044855 },
  { label: '一雪前耻', value: 1030000000044856 },
  { label: '749局', value: 1030000000044857 },
  { label: '穿过月亮的旅行', value: 1030000000044858 },
  { label: '哈尔的移动城堡', value: 1030000000044859 },
  { label: '灿烂的她', value: 1030000000044860 },
  { label: '白蛇：浮生', value: 1030000000044861 },
  { label: '变形金刚：起源', value: 1030000000044862 },
  { label: '鸿蒙', value: 1030000000012819 },
  { label: '鸿蒙 NEXT', value: 1030000000044863 },
  { label: '鸿蒙 4.0', value: 1030000000044864 },
  { label: '鸿蒙 4.2', value: 1030000000044865 },
  { label: '鸿蒙 3.1 Release', value: 1030000000044866 },
  { label: '鸿蒙 NEXT鸿蒙星河版', value: 1030000000044868 },
  { label: '鸿蒙 NEXT5.0', value: 1030000000044867 },
]

export const ENTITY_TYPE_OPTIONS = [
  { label: '关键词', value: 'keywords' },
  { label: '汽车品牌', value: 'car-brand' },
  { label: '车系', value: 'vehicle_series' },
  { label: '品牌', value: 'brand' },
  { label: '操作系统', value: 'operating_system' },
  { label: '手机机型', value: 'device' },
  { label: '终端AI助手', value: 'ai_assistant' },
  { label: '零售商超', value: 'supermarket' },
  { label: '车型', value: 'car_model' },
  { label: '手机游戏', value: 'game' },
  { label: '商品', value: 'item' },
  { label: '水果', value: 'fruit' },
  { label: '零食', value: 'snack' },
  { label: 'IP', value: 'ip' },
  { label: '综艺', value: 'varity_show' },
  { label: '电视剧', value: 'tv_series' },
  { label: '电影', value: 'film' },
  { label: '动漫', value: 'anime' },
  { label: '短剧', value: 'short_play' },
  { label: '小说', value: 'novel' },
  { label: '体育赛事', value: 'sports_events' },
  { label: '娱乐明星', value: 'entertainers' },
  { label: '体育明星', value: 'sports_stars' },
  { label: '网红', value: 'internet_celebrity' },
  { label: 'AI 产品', value: 'ai_product' },
  { label: 'APP', value: 'app' },
  { label: '学校', value: 'school' },
  { label: '医院', value: 'hospital' },
  { label: '公司', value: 'company' },
  { label: '汽车厂商', value: 'car-mfr' },
  { label: '单机游戏', value: '3a-game' },
  { label: '晚会', value: 'evening_party' },
]

export const MENU_KEY_NAME_MAP = {
  market_sensor: '奇异探测',
  mobile_app: 'APP 全息',
  social_marketing: '社媒智数',
  tipping_point: '奇异风口',
  ai: 'AI 全息',
}

export const TIME_MODULE_KEY_NAME_MAP: Record<string, string> = {
  market_psych: '市场情绪',
  market_risk: '市场风险',
  event_detection: '大事件库',
  user_scale_behavior: '用户规模/用户行为/AI产品概况-APP&WEB',
  user_profile: '用户画像',
  ai_overview_gpts: 'AI产品概况-GPTs',
  ai_overview_discord: 'AI产品概况-AI Discord',
  ai_overview_type_dist: 'AI产品概况-各产品类型应用数量总数/各产品类型用户分布',
  app: '用户规模-行业/头部公司/APP 用户行为/增长分析-行业/头部公司/APP/新上线 APP增长分析-整体-人均单日使用时长 终端分析 用户画像',
  app_overall: '用户规模-整体、增长分析-整体-活跃用户数',
  influence_analysis_dpha: '传播分析',
  brand_awareness: '品牌心智-需求认知',
  circle_perspective: '圈层透视',
  kol: 'KOL',
  exploding_topics: '风口概况/发现风口/风口趋势/风口路径发现爆品-AI',
  discover_products: '发现爆品-电商行业',
}

export const DATE_TYPE_MAP: Record<string, string> = {
  0: '日',
  1: '周',
  2: '月',
  3: '季',
  4: '半年',
  5: '年',
}

export const ALL_METRIC_INFO_LIST = [
  { label: '付费会员规模', value: 'pvm' },
  { label: '付费率', value: 'cr' },
  { label: '付费类型', value: 'pt' },
  { label: '基础画像', value: 'user_profile' },
  { label: '地域分布', value: 'geo_distribution' },
  { label: '消费分析', value: 'consumer_analysis' },
  { label: '兴趣分布', value: 'interest_distribution' },
  { label: '终端品牌活跃分布', value: 'dbad' },
  { label: '品牌分布', value: 'brand_distribution' },
  { label: '终端价格段分布', value: 'dprd' },
  { label: '活跃率', value: 'ar' },
  { label: '活跃渗透率', value: 'apr' },
  { label: '日均活跃用户数', value: 'adau' },
  { label: '日均活跃渗透率', value: 'adapr' },
  { label: '总使用时长', value: 'tut' },
  { label: '总使用次数', value: 'tnu' },
  { label: '人均单日使用时长', value: 'adtu' },
  { label: '人均单日使用次数', value: 'adnu' },
  { label: '月日均时段分布', value: 'mdhd' },
  { label: '活跃用户留存率', value: 'aurr' },
  { label: '新用户留存率', value: 'nurr' },
  { label: '人均单次使用时长', value: 'astu' },
  { label: '调用量', value: 'invoked_volume' },
  { label: '活跃用户数', value: 'au' },
  { label: '销量', value: 'sales' },
  { label: '销售额', value: 'sales_revenue' },
  { label: '商品单价', value: 'price_analyses' },
  { label: '价格弹性', value: 'price_elasticity' },
  { label: '销量市场份额', value: 'sales_market_share' },
  { label: '销售额市场份额', value: 'gmv_market_share' },
  { label: '消费偏好', value: 'consumer_preference' },
  { label: '消费群体分布', value: 'consumer_group_distribution' },
  { label: '线上消费水平', value: 'online_consumption_level' },
  { label: '活跃度分布', value: 'activity_distribution' },
  { label: '设备分布', value: 'device_distribution' },
  { label: '市场竞争度', value: 'market_competition' },
  { label: '市场集中度', value: 'market_cr' },
  { label: '价格段', value: 'price_range' },
  { label: '量价关系', value: 'price_demand_corr' },
  { label: '用户旅程', value: 'customer_journey' },
  { label: '性别年龄分布', value: 'gender_age_distribution' },
  { label: '地区分布', value: 'region_distribution' },
  { label: '城市等级', value: 'city_tier' },
  { label: '资产负债表', value: 'balance_sheet' },
  { label: '利润表', value: 'income' },
  { label: '现金流量表', value: 'cash_flow' },
  { label: '收支结构', value: 'income_exp_structure' },
  { label: '收入利润总额', value: 'total_income_profit' },
  { label: '盈利能力', value: 'profit_ability' },
  { label: '营运能力', value: 'operational_ability' },
  { label: '偿债能力', value: 'debt_ability' },
  { label: '企业发展能力', value: 'business_growth' },
  { label: '经营杠杆', value: 'operating_levarage' },
  { label: '财务杠杆', value: 'financial_leverage' },
  { label: '参与度', value: 'engagement' },
  { label: '扩增率', value: 'growth_rate' },
  { label: '互动率', value: 'interaction_rate' },
  { label: '合作 KOL 数量', value: 'coop_kol_cnt' },
  { label: '关联小店数', value: 'shop_cnt' },
  { label: '产品讨论', value: 'product_discussion' },
  { label: 'AI话题分析', value: 'ai_topic_analysis' },
  { label: '社媒声量', value: 'social_buzz' },
  { label: '社媒互动量', value: 'social_interaction' },
  { label: '声量', value: 'buzz' },
  { label: '情绪', value: 'sentiment' },
  { label: '热力图', value: 'heatmap' },
  { label: '事件库', value: 'event_gallary' },
  { label: '总声量', value: 'total_buzz' },
  { label: '总互动量', value: 'total_interaction' },
  { label: '净情感度(NSR)', value: 'nsr' },
  { label: '数据源分布', value: 'data_repo_distribution' },
  { label: '情感分布', value: 'sentiment_distribution' },
  { label: '声量 Top10 区域', value: 'top10_buzz_region' },
  { label: '传播分析', value: 'communi_analysis' },
  { label: '渠道分析', value: 'channel_analysis' },
  { label: '地区分析', value: 'regional_analysis' },
  { label: '概况', value: 'overview' },
  { label: '定制事件', value: 'custom_events' },
  { label: '风险竞争格局', value: 'risk_competition' },
  { label: '泛汽车用户', value: 'general_automobile_users' },
  { label: '汽车KOL', value: 'automobile_kols' },
  { label: '车主APP', value: 'automobile_apps' },
  { label: '社媒粉丝量', value: 'social_followers' },
  { label: '品牌形象', value: 'brand_reputation' },
  { label: '车系印象', value: 'series_reputation' },
  { label: '投诉评价', value: 'complaint_feedback' },
  { label: '负面情绪', value: 'negative_sentiment' },
  { label: '手机品牌-型号分布', value: 'phone_brand_model_distribution' },
  { label: '车主购车目的分布', value: 'car_purchase_purpose_distribution' },
  { label: '车主选车理由提及率', value: 'car_selection_reason_mention_rate' },
  { label: '用户爱好标签 TOP 10', value: 'user_hobby_tag_top10' },
  { label: '购车年限', value: 'car_purchase_years' },
  { label: '发表量', value: 'buzz' },
  { label: '互动量', value: 'interaction' },
  { label: 'KOL 数量', value: 'kol_cnt' },
  { label: '用户画像', value: 'user_profile' },
  { label: '粉丝数', value: 'fans_cnt' },
  { label: '直播平均观看人次', value: 'live_avg_pv' },
  { label: '直播分钟带货产出', value: 'rpm' },
  { label: '社媒覆盖率', value: 'social_coverage_rate' },
  { label: '触达率', value: 'ctr' },
  { label: '热点事件', value: 'hot_events' },
  { label: '热度峰值', value: 'hot_peak' },
  { label: '直播人气峰值', value: 'live_peak' },
  { label: '短视频播放量峰值', value: 'short_video_peak' },
  { label: '最高触达率', value: 'top_ctr' },
  { label: '品牌推广次数', value: 'brand_promotion_cnt' },
  { label: '品牌浏览量', value: 'brand_pv' },
  { label: '转化率', value: 'conversion_rate' },
  { label: '出单商品数', value: 'order_product_cnt' },
  { label: '关联直播数', value: 'related_live_cnt' },
  { label: '关联视频数', value: 'related_video_cnt' },
  { label: '关联达人数', value: 'related_kol_cnt' },
  { label: '品牌粉丝力', value: 'brand_fan_power' },
  { label: '品牌影响力', value: 'brand_influence' },
  { label: '内容效果分析', value: 'content_effective_analysis' },
  { label: '内容策略', value: 'content_strategy' },
  { label: '营销认知', value: 'marketing_perception' },
  { label: '用户粘性', value: 'us' },
  { label: '用户留存', value: 'ur' },
  { label: '用户流失', value: 'ucr' },
  { label: '来源去向', value: 'source_destination' },
  { label: '网络流量', value: 'network_traffic' },
  { label: '受众规模', value: 'audience_size' },
  { label: '需求认知', value: 'needs_cognition' },
  { label: '购买决策因素', value: 'purchase_decision' },
  { label: '热点分析', value: 'hotspot_analysis' },
  { label: '热门活动', value: 'popular_activities' },
  { label: '爆点概率', value: 'burst_probability' },
  { label: '圈层策略', value: 'circle_strategy' },
  { label: '圈层全景', value: 'circle_panorama' },
  { label: '裂变效应', value: 'fission_effect' },
  { label: '品牌占有率', value: 'brand_share' },
  { label: '风险矩阵', value: 'risk_matrix' },
  { label: '风险地图', value: 'risk_map' },
  { label: '圈层影响力', value: 'circle_influence' },
  { label: '品类关注度', value: 'category_attention' },
  { label: 'Top10国家分布', value: 'country' },
  { label: '性别分布', value: 'gender' },
  { label: '年龄分布', value: 'age' },
  { label: '家庭规模', value: 'family_size' },
  { label: '收入水平', value: 'income_level' },
  { label: '就业情况', value: 'employment_status' },
  { label: '受教育程度', value: 'education_level' },
  { label: '风险巡检', value: 'risk_inspection' },
  { label: 'KOL 智选', value: 'kol_smart_selection' },
  { label: '数据趋势', value: 'data_trend' },
  { label: '数据集', value: 'dateset' },
  { label: '数据用量', value: 'data_usage' },
  { label: 'API', value: 'api' },
  { label: '对话数', value: 'dialogue_count' },
  { label: '总用户数', value: 'total_users' },
]

export const ALL_MODULE_INFO_LIST = [
  { label: '市场情绪', value: 'market_psych' },
  { label: '风口概念', value: 'exploding_topics' },
  { label: '增长预测', value: 'prediction' },
  { label: '用户规模', value: 'user_scale' },
  { label: '实时探测', value: 'live_detection' },
  { label: '市场心理', value: 'market_psych' },
  { label: '事件探测', value: 'event_detection' },
  { label: '传播分析', value: 'influence_analysis' },
  { label: '品牌心智', value: 'brand_awareness' },
  { label: '内容引擎', value: 'content_engine' },
  { label: '圈层透视', value: 'circle_perspective' },
  { label: 'KOL', value: 'kol' },
  { label: 'APP 概况', value: 'overview' },
  { label: 'APP 详情', value: 'app_detail' },
  { label: '用户参与', value: 'user_behavior' },
  { label: '增长分析(移动应用)', value: 'growth_analysis' },
  { label: '付费分析', value: 'payment_analysis' },
  { label: '终端分析', value: 'device_analysis' },
  { label: '用户画像(移动应用)', value: 'user_profile' },
  { label: '整体概况', value: 'overview' },
  { label: '电商详情', value: 'ec_detail' },
  { label: '销量分析', value: 'sales_analysis' },
  { label: '销售额分析', value: 'revenue_analysis' },
  { label: '竞争分析', value: 'competitive_analysis' },
  { label: '量价分析(电商)', value: 'aov_analysis' },
  { label: '用户旅程', value: 'customer_journey' },
  { label: '成交画像', value: 'user_profile' },
  { label: '汽车概况', value: 'overview' },
  { label: '汽车详情', value: 'vehicle_detail' },
  { label: '用户分析', value: 'user_analysis' },
  { label: '用户口碑', value: 'user_reputation' },
  { label: '量价分析(汽车)', value: 'aov_analysis' },
  { label: '用户画像(汽车)', value: 'user_profile' },
  { label: '财务报表', value: 'financial_statement' },
  { label: '财务表现力', value: 'financial_performance' },
  { label: '资产贝塔', value: 'asset_beta' },
  { label: 'Z-Score', value: 'z_score' },
  { label: '杜邦分析', value: 'dupont_analysis' },
  { label: 'EVA 分析', value: 'eva_analysis' },
  { label: '报告中心', value: 'research_report' },
  { label: '图表中心', value: 'data_chart' },
  { label: 'Real-time AI 探测', value: 'realtime_detect' },
  { label: '数据量', value: 'quantity_report' },
  { label: '数据源', value: 'overview' },
  { label: '数据质量', value: 'quality_report' },
  { label: '即时调研', value: 'word_explorer' },
  { label: 'DataGPT', value: 'datagpt' },
  { label: 'AI Report', value: 'ai_report' },
  { label: '用户心理', value: 'customer_psych' },
  { label: '用户行为', value: 'user_behavior' },
  { label: '用户画像', value: 'user_profile' },
  { label: '用户参与', value: 'user_engagement' },
  { label: '数据看板', value: 'dashboard' },
  { label: '引爆点', value: 'detonation_point' },
  { label: '营销效率', value: 'marketing_roi' },
  { label: '市场风险', value: 'market_risk' },
  { label: '竞争格局', value: 'competitive_landscape' },
  { label: '概念详情', value: 'topics_detail' },
  { label: '活动详情', value: 'activity_detail' },
  { label: '爆点详情', value: 'burst_detail' },
  { label: '事件详情', value: 'event_detail' },
  { label: 'AI 详情', value: 'ai_detail' },
  { label: '预测详情', value: 'prediction_detail' },
  { label: '数据中心', value: 'data_center' },
  { label: '发现风口', value: 'discover_topics' },
  { label: '风口概况', value: 'exploding_topics_overview' },
  { label: '风口图谱', value: 'exploding_topics_map' },
  { label: '发现爆品', value: 'discover_products' },
  { label: '增长预测', value: 'prediction' },
  { label: '增长预测详情', value: 'prediction_detail' },
]

export const ALL_TARGET_INFO_LIST = [
  { label: 'APP', value: 'app' },
  { label: '行业', value: 'industry' },
  { label: 'AI 产品', value: 'ai' },

]
