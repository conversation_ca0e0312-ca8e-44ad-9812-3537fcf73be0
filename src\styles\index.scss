@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;

* {
  box-sizing: border-box;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  font-size: 16px;
  padding: 0;
  margin: 0;
}

body {
  position: relative;
  margin: 0;
  min-height: 100vh;
  width: 100%;
  height: 100%;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

#app {
  width: 100%;
  height: 100%;
}

::-webkit-scrollbar {
  background-color: rgba(255, 255, 255, 0);
  width: 10px;
  padding: 0 5px;
}

::-webkit-scrollbar-thumb {
  @apply bg-black/02 dark:bg-white/10;
  border-radius: 48px;
  // backdrop-filter: blur(20px);
}

#nprogress .bar {
  @apply \!bg-primary;
}

.el-button + .el-button {
  margin-left: 0px !important;
}

a {
  @apply text-primary decoration-none;
}
