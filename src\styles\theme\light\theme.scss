$--colors: (
  'white': #f8f8f7,
  'black': #000000,
  'primary': (
    'base': #f24c42,
  ),
  'success': (
    'base': #049541,
  ),
  'warning': (
    'base': #ffbf5f,
  ),
  'danger': (
    'base': #ff0000,
  ),
  'error': (
    'base': #ff0000,
  ),
  'info': (
    'base': #a0b5ec,
  ),
);

@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // do not use same name, it will override.
  $colors: $--colors,
  $text-color: (
    'regular': #000000,
    'secondary': #6c757d,
  ),
  $border-radius: ('base': 8px, 'small': 4px, 'round': 24px, 'circle': 100%),
  $popper: ('border-radius': 8px),
  $card: ('border-radius': 8px),
  $box-shadow: (
    '': (
      rgba(0, 0, 0, 0.1) 0px 4px 6px -1px,
      rgba(0, 0, 0, 0.06) 0px 2px 4px -1px,
    ),
    'light': (
      rgba(0, 0, 0, 0.1) 0px 4px 6px -1px,
      rgba(0, 0, 0, 0.06) 0px 2px 4px -1px,
    ),
    'lighter': (
      rgba(0, 0, 0, 0.1) 0px 4px 6px -1px,
      rgba(0, 0, 0, 0.06) 0px 2px 4px -1px,
    )
  ),
  $pagination: ('border-radius': 8px),
  $mask-color:(
    '': rgba(255, 255, 255),
  )
);

// custom dark variables
@use '../dark/theme.scss';
