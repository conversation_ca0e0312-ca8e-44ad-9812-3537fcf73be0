type MenuKey = 'market_sensor' | 'ai' | 'mobile_app' | 'social_marketing' | 'tipping_point'
type ModuleKey = 'market_psych' | 'market_risk' | 'event_detection' | 'user_scale_behavior' | 'user_profile' | 'app' | 'app_overall' | 'influence_analysis_dpha' | 'brand_awareness' | 'circle_perspective' | 'kol' | 'exploding_topics' | 'discover_products' | 'ai_overview_type_dist' | 'ai_overview_app_web' | 'ai_overview_gpts' | 'ai_overview_discord'

export interface MenuTimeSetting {
  menu: MenuKey
  module: ModuleKey
  date_type: number
  start_time: string
  end_time: string
}

function useData() {
  async function getMenuTimeSetting() {
    const query = {
      methodName: 'menu_time_settings',
      offset: 0,
      limit: 999,
    }
    const res = await loadV2<MenuTimeSetting[]>({ query })
    res.data = res.data.filter(item => item.module !== 'event_detection')
    return res
  }

  async function updateMenuTime(params: Partial<MenuTimeSetting>) {
    const res = await talon<PERSON>pi({
      methodName: 'update_menu_time',
      ...params,
    })
    return res
  }

  return {
    getMenuTimeSetting,
    updateMenuTime,
  }
}

export default useData
