import type { DataRequestQuery } from '@/apis/data'
import { loadV2, miscApi } from '@/apis/data'
import type { AccountListItem, AccountPermissionConfig } from './types'
import { transformToCascaderOptions } from '@/utils/transform'

interface CreateAccountParams {
  username: string
  password: string
  account: string
  company: string
  effective_start_at: string
  effective_end_at: string
  industry_code: number
  industry_level: number
  account_type: string
  login_type: number
}

interface UpdateAccountParams {
  user_code: string
  is_enabled: string
  effective_start_at: string
  effective_end_at: string
  account_type: string
  industry_code: number
  industry_level: number
  login_type: number
}

interface ExplodingTopicsCategory {
  category_code: number
  category_name: string
  category_level: number
  category_parent: number
  region: string
  enable: boolean
  sort: number
  created_at: string
  updated_at: string
}

interface UserDownloadLimit {
  download_cnt_limit: number
  download_row_limit: number
}

interface EntityItemResponse {
  entity_code: number
  entity_name: string
  entity_type_en: string
  entity_type_cn: string
  industry_code: number
  industry_name: string
  industry_level: number
  similarity_score: number
}

export function useAccountData() {
  // 获取账号列表
  async function getAccountList() {
    const { data } = await talonApi<AccountListItem[]>({
      methodName: 'user_list',
    })
    return {
      data,
    }
  }
  // 创建账号
  async function createAccount(params: CreateAccountParams) {
    const res = await talonApi({
      methodName: 'create_user',
      ...params,
    })
    return res
  }
  // 更新账号信息
  async function updateAccount(params: UpdateAccountParams) {
    const res = await talonApi({
      methodName: 'update_user',
      user_code: params.user_code,
      is_enabled: params.is_enabled,
      effective_start_at: params.effective_start_at,
      effective_end_at: params.effective_end_at,
      account_type: params.account_type,
      industry_code: params.industry_code,
      industry_level: params.industry_level,
      login_type: params.login_type,
    })
    return res
  }

  async function updateUserAuthConfig(userCode: string, config: any) {
    const res = await talonApi({
      methodName: 'set_permission',
      user_code: userCode,
      permission_config: config,
    })
    return res
  }

  async function unbindWechat(userCode: string) {
    const res = await talonApi({
      methodName: 'clean_wx_binding',
      user_code: userCode,
    })
    return res
  }
  async function unbindDevice(userCode: string) {
    const res = await talonApi({
      methodName: 'clean_device_binding',
      user_code: userCode,
    })
    return res
  }

  async function getUserAuthConfig(userCode: string) {
    const res = await talonApi<AccountPermissionConfig>({
      methodName: 'get_permission',
      user_code: userCode,
    })
    return res
  }

  // 获取市场情绪品牌行业选项
  async function getBrandsIndustry() {
    const queryParams = {
      query: {
        methodName: 'pulse_mp_industry_list',
        limit: 1000,
        filters: [],
      },
    }
    const { data } = await loadV2(queryParams)
    const options = transformToCascaderOptions(data, {
      labelKey: 'industry_name',
      valueKey: 'mp_industry_code',
      parentKey: 'industry_parent',
    })
    return options
  }

  // 获取奇异风口赛道大类选项
  async function getExplodingTopicsCategoryLv1() {
    const queryParams = {
      query: {
        methodName: 'exploding_topics_category',
        limit: 1000,
        order: { 'dbt.sort': 'asc' },
        filters: [
          {
            member: 'dbt.enable',
            operator: 'equals',
            values: [true],
          },
          {
            member: 'dbt.category_level',
            operator: 'equals',
            values: [1],
          },
        ],
      },
    }
    const { data } = await loadV2<ExplodingTopicsCategory[]>(queryParams)
    const options = data.map((item) => {
      return {
        label: item.category_name,
        value: item.category_code.toString(),
        region: item.region,
      }
    })
    return options
  }

  // 获取市场风险品牌列表
  async function getRiskBrand() {
    const queryParams: any = {
      query: {
        methodName: 'pulse_mp_entity_list',
        limit: 3000,
        filters: [],
        order: {
          rw: 'asc',
        },
      },
    }
    const { data } = await loadV2(queryParams)
    const options = data.map((item: any) => {
      return {
        label: item.entity_name,
        value: item.entity_code,
      }
    })
    return options
  }

  // 获取社媒品牌列表
  async function getSMBrand(searchInput?: string) {
    const queryParams: any = {
      query: {
        methodName: 'buzz_entity',
        limit: 3000,
        filters: [],
        order: {
          rw: 'asc',
        },
      },
    }
    if (searchInput) {
      queryParams.query.filters.push({
        member: 'entity_name',
        operator: 'contains',
        values: [searchInput],
      })
    }
    const { data } = await loadV2(queryParams)
    const options = data.map((item: any) => {
      return {
        label: item.entity_name,
        value: item.entity_code,
      }
    })
    return options
  }

  async function getSMSaEntity() {
    const query: DataRequestQuery = {
      methodName: 'buzz_ia_satisfaction_similarity_query',
      limit: 3000,
      offset: 0,
      filters: [
        {
          member: 'entity_name',
          operator: 'similarity',
          values: [''],
        },
      ],
    }
    const { data } = await miscApi<EntityItemResponse[]>({ query })
    return data.map((item) => {
      return {
        label: item.entity_name,
        value: item.entity_code,
      }
    })
  }

  // 获取市场情绪品牌行业选项
  async function getSMIndustry() {
    const queryParams = {
      query: {
        methodName: 'buzz_industry',
        order: {
          sort: 'asc',
        },
        filters: [],
        limit: 2000,
      },
    }
    const { data } = await loadV2(queryParams)
    const options = transformToCascaderOptions(data, {
      labelKey: 'industry_name',
      valueKey: 'industry_code',
      parentKey: 'industry_parent',
    })
    return options
  }

  /**
   * 获取用户的数据下载次数和行数上限
   */
  async function getUserDownloadLimit(userCode: string) {
    const res = await talonApi<UserDownloadLimit>({
      methodName: 'user_download_limit',
      user_code: userCode,
    })
    return res
  }

  /**
   * 更新用户的数据下载次数和行数上限
   */
  async function updateUserDownloadLimit(userCode: string, params: UserDownloadLimit) {
    const res = await talonApi({
      methodName: 'update_user_download_limit',
      user_code: userCode,
      download_cnt_limit: params.download_cnt_limit,
      download_row_limit: params.download_row_limit,
    })
    return res
  }

  return {
    getAccountList,
    createAccount,
    updateAccount,
    updateUserAuthConfig,
    getUserAuthConfig,
    getBrandsIndustry,
    getExplodingTopicsCategoryLv1,
    getRiskBrand,
    getSMIndustry,
    getSMBrand,
    getSMSaEntity,
    getUserDownloadLimit,
    updateUserDownloadLimit,
    unbindWechat,
    unbindDevice,
  }
}
