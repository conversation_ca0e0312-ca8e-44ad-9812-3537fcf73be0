export interface IndustryList {
  industry_code: number
  industry_name: string
}

export interface EntityIndustryMapping {
  created_at: string
  created_by: number
  entity_code: number
  entity_name: string
  id: number
  industry_code: number
  industry_name: string
  updated_at: string
  updated_by: number
}

export function useData() {
  // 新增实体行业映射
  async function uploadEntityIndustryMapping(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'sm_entity_industry_mapping_batch_create')
    formData.append('file', file)
    const res = await talonApi(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  // 查询实体行业映射
  async function getEntityIndustryMappingList(params: {
    industry_code?: number
    searchValue?: string
  }) {
    const res = await talonApi<EntityIndustryMapping[]>({
      methodName: 'sm_entity_industry_mapping_list',
      industry_code: params.industry_code,
      entity_name: params.searchValue,
    })
    return res
  }

  // 编辑实体行业映射
  async function editEntityIndustryMapping(data: {
    id: number
    industry_name: string
    entity_name: string
  }) {
    const res = await talonApi({
      methodName: 'sm_entity_industry_mapping_update',
      ...data,
    })
    return res
  }

  // 删除实体行业映射
  async function deleteEntityIndustryMapping(id: number) {
    const res = await talonApi({
      methodName: 'sm_entity_industry_mapping_delete',
      id,
    })
    return res
  }

  return {
    uploadEntityIndustryMapping,
    getEntityIndustryMappingList,
    editEntityIndustryMapping,
    deleteEntityIndustryMapping,
  }
}
