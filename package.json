{"name": "xsignal-frontend-config", "version": "1.0.0", "description": "", "author": "", "license": "ISC", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc", "lint-check": "eslint \"**/*.{vue,js,ts,tsx}\"", "check": "npm run type-check && npm run lint-check"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@element-plus/icons-vue": "^2.3.1", "handsontable": "6.2.2", "unplugin-icons": "^0.19.3", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config-vue": "^0.38.5", "@iconify-json/ep": "^1.2.2", "@intlify/unplugin-vue-i18n": "^0.12.2", "@kangc/v-md-editor": "^2.3.16", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.3", "@types/uuid": "^9.0.1", "@vicons/carbon": "^0.12.0", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vueuse/core": "^10.1.2", "axios": "^1.6.2", "dayjs": "^1.11.7", "echarts": "^5.4.3", "echarts-composables": "^0.2.1", "element-plus": "^2.7.7", "eslint": "^8.39.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-vue": "^9.11.0", "highlight.js": "^11.8.0", "js-cookie": "^3.0.1", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.0.34", "sass": "^1.62.0", "typescript": "^4.9.3", "unocss": "^0.51.5", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "uuid": "^9.0.0", "vite": "^4.2.0", "vite-plugin-eslint": "^1.8.1", "vite-svg-loader": "^4.0.0", "vue": "^3.3.4", "vue-i18n": "9", "vue-router": "^4.1.6", "vue-tsc": "^1.2.0"}}