<template>
  <div class="flex flex-1 flex-col gap-16">
    <div class="flex gap-16">
      <el-input
        v-model="searchKeyword"
        class="mr-auto"
        placeholder="请输入实体类型名称"
        :prefix-icon="Search"
        style="width: 240px;"
        @keyup.enter="onSearch"
      />
      <el-button size="default" type="primary" @click="onAdd">新增实体类型</el-button>
    </div>
    <DataTable
      class="flex-1"
      :columns="columns"
      :show-index-column="false"
      :status="tableStatus"
      :table-data="tableData"
    />

    <!-- 新增/编辑实体类型弹窗 -->
    <EditEntityTypeModal
      v-model:visible="editModalVisible"
      :entity-data="currentEditEntity"
      @confirm="onConfirm"
    />
  </div>
</template>

<script setup lang="tsx">
import { Search } from '@element-plus/icons-vue'
import { ElButton, ElPopconfirm } from 'element-plus'
import type { Column } from '@/components/DataTable/DataTable.vue'
import type { EntityTypeTable } from '../hooks/useData'
import { useData } from '../hooks/useData'
import EditEntityTypeModal from './EditEntityTypeModal.vue'

const {
  addOrEditEntityType,
  deleteEntityType,
  getEntityTypeList,
} = useData()

const searchKeyword = ref('')

const tableStatus = ref<DataStatus>('loading')
const tableData = ref<EntityTypeTable[]>([])

const columns: Column<EntityTypeTable>[] = [
  {
    prop: 'entity_type_code',
    label: '实体类型编码',
  },
  {
    prop: 'entity_type_cn',
    label: '实体类型',
  },
  {
    prop: 'entity_type_en',
    label: '实体类型 key',
  },
  {
    prop: 'sort',
    label: '排序',
  },
  {
    prop: 'updated_at',
    label: '最新操作时间',
    width: 180,
  },
  {
    prop: 'updated_by',
    label: '最新操作人',
  },
  {
    prop: 'operation',
    label: '操作',
    minWidth: 100,
    fixed: 'right',
    customRenderer: (scope) => {
      return (
        <div class="flex">
          <ElButton type='primary' size='small' text onClick={() => onEdit(scope.row)}>编辑</ElButton>
          <ElPopconfirm
            title="确认删除该记录吗？"
            width="200"
            onConfirm={() => onDelete(scope.row)}
            v-slots={{
              reference: () => (
                <ElButton type='info' size='small' text>删除</ElButton>
              ),
            }}
          >
          </ElPopconfirm>
        </div>
      )
    },
  },
]

// 新增/编辑弹窗相关状态
const editModalVisible = ref(false)
const currentEditEntity = ref<any>({})

// 加载数据
async function loadData() {
  tableStatus.value = 'loading'
  const { data } = await getEntityTypeList(searchKeyword.value.trim())
  tableData.value = data
  tableStatus.value = data.length ? 'normal' : 'empty'
}

function onSearch() {
  loadData()
}

function onAdd() {
  currentEditEntity.value = {
    id: 0,
    entity_type_cn: '',
    entity_type_en: '',
    entity_type_code: '',
    sort: undefined,
  }
  editModalVisible.value = true
}

// 处理编辑按钮点击事件
function onEdit(row: EntityTypeTable) {
  currentEditEntity.value = row
  editModalVisible.value = true
}

// 处理删除按钮点击事件
async function onDelete(row: EntityTypeTable) {
  const { error_code, error_msg } = await deleteEntityType(row.id)
  if (error_code === 0) {
    ElMessage.success('删除成功')
    await loadData()
  }
  else {
    ElMessage.error(`删除失败，原因：${error_msg}`)
  }
}

// 处理编辑确认事件
async function onConfirm(formData: Partial<EntityTypeTable>) {
  console.log(formData)
  const { id, entity_type_cn, entity_type_en, entity_type_code, sort } = formData
  if (!entity_type_cn?.trim() || !entity_type_en?.trim()) {
    ElMessage.error('请输入完整信息')
    return
  }
  if (id && entity_type_code) {
    // 编辑操作
    if (!sort) {
      ElMessage.error('请输入完整信息')
      return
    }
  }
  const { error_msg, error_code } = await addOrEditEntityType({
    id: id || 0,
    entity_type_cn: entity_type_cn?.trim() || '',
    entity_type_en: entity_type_en?.trim() || '',
    entity_type_code: entity_type_code || 0,
    sort: sort || 0,
  })
  if (error_code === 0) {
    ElMessage.success('保存成功')
    editModalVisible.value = false
    loadData()
  }
  else {
    ElMessage.error(`保存失败，原因：${error_msg}`)
  }
}

onMounted(() => {
  loadData()
})
</script>
