<template>
  <el-dialog
    destroy-on-close
    :model-value="modelValue"
    :title="title"
    width="500px"
  >
    <div class="flex flex-col gap-16">
      <div class="flex items-center justify-between">
        <span>请上传Excel文件：</span>
        <el-link type="primary" :underline="true" @click="downloadTemplate">下载模板</el-link>
      </div>

      <el-upload
        accept=".xlsx"
        action="#"
        :auto-upload="false"
        class="upload-demo"
        drag
        :limit="1"
        :on-change="handleFileChange"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          拖拽文件到此处或 <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只支持 xlsx 格式文件
          </div>
        </template>
      </el-upload>
    </div>

    <template #footer>
      <span class="flex gap-8 justify-end">
        <el-button @click="closeDialog">取消</el-button>
        <el-button :disabled="!uploadFile" type="primary" @click="confirmUpload">确认上传</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '导入数据',
  },
  templateUrl: {
    type: String,
    required: true,
  },
  templateFilename: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'upload', 'close'])

const uploadFile = ref<File | null>(null)

function handleFileChange(file: any) {
  if (file && file.raw) {
    uploadFile.value = file.raw
  }
  else {
    uploadFile.value = null
  }
}

function closeDialog() {
  emit('update:modelValue', false)
  emit('close')
  uploadFile.value = null
}

function confirmUpload() {
  if (!uploadFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  emit('upload', uploadFile.value)
}

// 下载模板
async function downloadTemplate() {
  try {
    const response = await fetch(props.templateUrl)
    const blob = await response.blob()

    const objectUrl = window.URL.createObjectURL(new Blob([blob], {
      type: 'application/octet-stream',
    }))

    const link = document.createElement('a')
    link.href = objectUrl
    link.download = props.templateFilename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(objectUrl)
  }
  catch (error) {
    console.error('下载模板失败:', error)
  }
}
</script>
