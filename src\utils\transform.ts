interface TransformToCascaderOptionsKeyMap {
  valueKey: string
  labelKey: string
  parentKey: string
  sortKey?: string
}

export function transformToCascaderOptions(data: any[], keyMap: TransformToCascaderOptionsKeyMap) {
  // 创建一个空对象用于存储每个分类的引用
  const categoryMap: { [categoryCode: string]: any } = {}

  // 将所有分类数据转换为CategoryData对象，并存储到categoryMap中
  data.forEach((item: Record<string, any>) => {
    const categoryData: any = {
      ...item,
    }
    categoryData[keyMap.valueKey] = categoryData[keyMap.valueKey].toString()
    categoryData[keyMap.parentKey] = categoryData[keyMap.parentKey]?.toString()
    if (keyMap.sortKey) {
      categoryData[keyMap.sortKey] = categoryData[keyMap.sortKey] as number
    }
    // 将分类数据添加到categoryMap中
    categoryMap[categoryData[keyMap.valueKey]] = categoryData
  })

  // 构建树状结构
  const categoryTree: any[] = []
  Object.values(categoryMap).forEach((categoryData) => {
    const parentCode = categoryData[keyMap.parentKey]

    if (parentCode && categoryMap[parentCode]) {
      // 如果有父分类，则将当前分类添加到父分类的children属性中
      const parentCategory = categoryMap[parentCode]
      if (parentCategory) {
        parentCategory.children = parentCategory.children || []
        parentCategory.children.push(categoryData)
      }
    }
    else {
      // 没有父分类，说明是根级分类，直接添加到categoryTree中
      categoryTree.push(categoryData)
    }
  })

  if (keyMap.sortKey) {
    const sortKey = keyMap.sortKey as string
    categoryTree.sort((a: any, b: any) => (a[sortKey]) - (b[sortKey]))
    categoryTree.forEach((item: any) => {
      item.children?.sort((a: any, b: any) => (a[sortKey]) - (b[sortKey]))
    })
  }

  return categoryTree
}
