<template>
  <el-cascader
    filterable
    :model-value="modelValue"
    :options="smIndustryOptions"
    :placeholder="placeholder"
    :props="cascaderProps"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { useAccountStore } from '@/stores/account'

const props = defineProps({
  modelValue: {
    type: Array as () => string[],
    required: true,
  },
  placeholder: {
    type: String,
    default: '全部',
  },
  props: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const { smIndustryOptions } = storeToRefs(useAccountStore())

const cascaderProps = computed(() => {
  return {
    expandTrigger: 'hover',
    label: 'industry_name',
    value: 'industry_code',
    checkStrictly: true,
    ...props.props,
  }
})

function onChange(value: string[]) {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
/* 可以在这里添加自定义样式 */
</style>
