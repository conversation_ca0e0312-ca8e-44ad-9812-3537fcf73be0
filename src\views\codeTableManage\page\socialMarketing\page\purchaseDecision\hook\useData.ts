export interface SmPurDecCodeTable {
  created_at: string
  created_by: null
  dimension: string
  filter_words: string
  id: number
  industry_code: number
  industry_name: string
  keywords: string
  type: string
  updated_at: string
  updated_by: number
}

export function useData() {
  async function getCodeTableList(industry_code: number) {
    const res = await talonApi<SmPurDecCodeTable[]>({
      methodName: 'sm_pur_dec_codetable_list',
      industry_code,
    })
    return res
  }

  async function updateCodeTable(data: SmPurDecCodeTable[]) {
    const res = await talonApi<{ rows: number }>({
      methodName: 'sm_pur_dec_codetable_update',
      data,
    })
    return res
  }

  async function addByXlsx(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'sm_pur_dec_codetable_batch_create')
    formData.append('file', file)
    const res = await talon<PERSON><PERSON>(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  return {
    getCodeTableList,
    updateCodeTable,
    addByXlsx,
  }
}
