export interface CpMpCodeTable {
  created_at: string
  created_by: null
  dimension: string
  id: number
  industry_code: number
  industry_name: string
  updated_at: string
  updated_by: number
  word: string
}

export function useData() {
  async function getCodeTableList(industry_code: number) {
    const res = await talon<PERSON>pi<CpMpCodeTable[]>({
      methodName: 'sm_mp_codetable_list',
      industry_code,
    })
    return res
  }

  async function updateCodeTable(data: CpMpCodeTable[]) {
    const res = await talonApi<{ rows: number }>({
      methodName: 'sm_mp_codetable_update',
      data,
    })
    return res
  }

  async function addByXlsx(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'sm_mp_codetable_batch_create')
    formData.append('file', file)
    const res = await talonApi(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  return {
    getCodeTableList,
    updateCodeTable,
    addByXlsx,
  }
}
