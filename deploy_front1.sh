#!/bin/bash
source ~/.bashrc
echo "[INFO] build and deploy to front1"

current_time=$(date +"%Y-%m-%d %H:%M:%S")
curl -X POST -H "Content-Type: application/json" \
    -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[Front] xsignal-frontend-config 于 $current_time 开始更新，如果稍后没有收到更新成功的消息，请联系 Jessie\"}}" \
    https://open.feishu.cn/open-apis/bot/v2/hook/04775be5-f9b5-4644-bb71-767c919186c3


echo "[INFO] get latest code from branch main"
rm -f src/components.d.ts
git pull
echo "[INFO] start installing node modules"
export NODE_OPTIONS="--max-old-space-size=4096"
pnpm install
echo "[INFO] start building"
pnpm build

# 20240528 如果上一句执行失败，则跳过并发送通知
if [ $? -ne 0 ]; then
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    curl -X POST -H "Content-Type: application/json" \
    -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[Front] xsignal-frontend-config 于 $current_time 更新失败，请联系 Jessie\"}}" \
    https://open.feishu.cn/open-apis/bot/v2/hook/04775be5-f9b5-4644-bb71-767c919186c3
    exit 1
fi

# 提取 git log
latest_log=$(git log -1 --pretty=format:"%s")

if [ -f "config-site.zip" ]; then
    echo "[INFO] old config-site.zip exists. remove it first"
    rm config-site.zip
fi
echo "[INFO] zip dist folder to config-site.zip"
zip -r config-site.zip dist/

echo "[INFO] send to prd"
scp config-site.zip root@**************:/root/website/
echo "[INFO] refresh nginx"
ssh root@************** 'cd /root/website/; unzip config-site.zip; rm -f config-site.zip; rm -rf config/*; mv dist/* config/; rm -rf dist; docker restart nginx'


current_time=$(date +"%Y-%m-%d %H:%M:%S")
curl -X POST -H "Content-Type: application/json" \
    -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[Front] xsignal-frontend-config 于 $current_time 更新成功，本次更新：$latest_log\"}}" \
    https://open.feishu.cn/open-apis/bot/v2/hook/04775be5-f9b5-4644-bb71-767c919186c3

echo "[INFO] all done"

