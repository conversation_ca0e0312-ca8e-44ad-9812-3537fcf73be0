<template>
  <DataTable
    v-model:page-option="pageOption"
    class="flex-1"
    :columns="columns"
    row-key="activity_code"
    :show-index-column="false"
    :status="tableStatus"
    :table-data="tableData"
    @page-change="loadData"
  />
</template>

<script setup lang="tsx">
import type { Column, PageOption } from '@/components/DataTable/DataTable.vue'
import DataTable from '@/components/DataTable/DataTable.vue'
import { ElTag } from 'element-plus'
import type { AdminActivity } from '../hooks/useData'
import { useData } from '../hooks/useData'

const { getAdminActivityList } = useData()

const pageOption = ref<PageOption>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

const statusMap = {
  QUEUED: ['生成中', 'warning'],
  NOT_STARTED: ['生成中', 'warning'],
  MANAGED: ['生成中', 'warning'],
  SUCCESS: ['已完成', 'success'],
  STARTED: ['生成中', 'warning'],
  STARTING: ['生成中', 'warning'],
  FAILURE: ['生成中', 'warning'],
  CANCELING: ['生成中', 'warning'],
  CANCELED: ['生成中', 'warning'],
} as const

const columns: Column<any>[] = [
  {
    label: '选择迁移',
    width: 100,
    customRenderer: ({ row }) => {
      return (
          <el-checkbox
            model-value={row.selected}
            onChange={(value: boolean) => {
              row.selected = value
            }}
          />
      )
    },
  },
  {
    prop: 'activity_code',
    label: 'Code',
    minWidth: 150,
  },
  {
    prop: 'activity_name',
    label: '活动名称',
    minWidth: 300,
  },
  {
    prop: 'start_date',
    label: '活动开始时间',
    minWidth: 150,
  },
  {
    prop: 'end_date',
    label: '活动结束时间',
    minWidth: 150,
  },
  {
    prop: 'status',
    label: '状态',
    minWidth: 100,
    customRenderer: (scope) => {
      return (
        <ElTag type={statusMap[scope.row.status as keyof typeof statusMap][1]}>
          {statusMap[scope.row.status as keyof typeof statusMap][0]}
        </ElTag>
      )
    },
  },
]
const tableStatus = ref<DataStatus>('loading')
const tableData = ref<AdminActivity[]>([])

async function loadData() {
  tableStatus.value = 'loading'

  const { data } = await getAdminActivityList((pageOption.value.currentPage - 1) * pageOption.value.pageSize)
  const { data: activityList, total } = data
  tableData.value = activityList
  pageOption.value.total = total

  tableStatus.value = 'normal'
}
loadData()

defineExpose({
  tableData,
  loadData,
})
</script>

<style scoped>
</style>
