export interface SmSaCodeTable {
  created_at: null
  created_by: null
  dimension: string
  filter_words: null
  id: number
  industry_code: number
  industry_level: number
  industry_parent: number
  keywords: string
  sort: number
  updated_at: null
  updated_by: null
}

export function useData() {
  async function getCodeTableList(industry_code: string) {
    const res = await talon<PERSON>pi<SmSaCodeTable[]>({
      methodName: 'sm_sa_codetable_list',
      industry_code: Number(industry_code),
    })
    return res
  }

  async function updateCodeTable(data: SmSaCodeTable[]) {
    const res = await talonApi<{ rows: number }>({
      methodName: 'sm_sa_codetable_update',
      data,
    })
    return res
  }

  async function addByXlsx(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'sm_sa_codetable_batch_create')
    formData.append('file', file)
    const res = await talon<PERSON>pi(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  return {
    getCodeTableList,
    updateCodeTable,
    addByXlsx,
  }
}
