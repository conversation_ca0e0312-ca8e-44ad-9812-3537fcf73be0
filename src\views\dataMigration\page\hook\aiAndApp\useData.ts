interface MigrationData {
  created_at: string
  dagster_run_id: null
  data_total: null
  entity_code: null
  id: number
  menu: number
  migration_date: string
  migration_period: number
  status: 1 | 2
  time_total: null
  user_code: number
}

interface MigrationLatestRun {
  endTime: number
  mode: string
  pipelineName: string
  runId: string
  startTime: number
  status: 'SUCCESS' | 'FAILURE' | string
  tags: any[]
}

interface MigrationListParams {
  type: 'time' | 'app'
  menu: 1 | 2 | 99 // 1: AI 全息, 2: APP 全息, 99: AI 钛媒体
  migration_period?: number // 迁移时间周期
}

type CreateMigrationParams = {
  migration_date?: string
} & Pick<MigrationListParams, 'menu' | 'migration_period'>

export function useData() {
  async function getMigrationList(params: MigrationListParams) {
    const res = await talonApi<MigrationData[]>({
      methodName: 'migration_list',
      ...params,
    })
    return res
  }

  async function createMigration(params: CreateMigrationParams) {
    const res = await talonApi({
      methodName: 'create_migration',
      ...params,
    })
    return res
  }

  async function getMigrationLatestRun(params: MigrationListParams) {
    const res = await talonApi<MigrationLatestRun>({
      methodName: 'migration_latest_run',
      ...params,
    })
    return res
  }

  return {
    getMigrationList,
    createMigration,
    getMigrationLatestRun,
  }
}
