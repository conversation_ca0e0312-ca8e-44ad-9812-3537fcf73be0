<template>
  <div class="flex flex-col gap-24 h-full">
    <div class="flex flex-shrink-0 gap-12 items-center">
      <div class="flex gap-8 items-center">
        <el-input
          v-model="searchKeyword"
          class="w-120"
          placeholder="请输入邮箱"
        >
          <template #suffix>
            <IEpSearch />
          </template>
        </el-input>
      </div>

      <div class="flex gap-8 items-center">
        <span class="text-14">账号状态</span>
        <el-select v-model="accountStatuFilter" placeholder="Select" style="width: 100px">
          <el-option label="全部" value="all" />
          <el-option label="正常" :value="true" />
          <el-option label="不可用" :value="false" />
        </el-select>
      </div>
      <div class="flex gap-8 items-center">
        <span class="text-14">账号类型</span>
        <el-select v-model="accountTypeFilter" placeholder="Select" style="width: 100px">
          <el-option label="全部" value="all" />
          <el-option v-for="item in ACCOUNT_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <el-button class="ml-auto" type="primary" @click="handleClickAdd">账号创建</el-button>
      <el-button @click="loadAccounts">刷新列表</el-button>
    </div>
    <el-table class="flex-1" :data="tableData">
      <el-table-column fixed="left" label="序号" type="index" width="60" />
      <el-table-column label="编码" prop="user_code" width="80" />
      <el-table-column label="邮箱" prop="account" width="180" />
      <el-table-column label="用户名" prop="user_name" width="150" />
      <el-table-column label="公司名称" prop="company_name" width="150" />
      <el-table-column label="行业" prop="industry_code" width="100">
        <template #default="scope">
          {{ findNodeById(industryOptions, 'mp_industry_code', scope.row.industry_code.toString())?.industry_name || '【异常数据】' }}
        </template>
      </el-table-column>
      <el-table-column label="账号类型" prop="account_type" width="100">
        <template #default="scope">
          {{ ACCOUNT_TYPE_OPTIONS.find(item => item.value === scope.row.account_type)?.label || '【异常数据】' }}
        </template>
      </el-table-column>
      <el-table-column label="登录方式" prop="login_type" width="120">
        <template #default="scope">
          {{ LOGIN_TYPE_OPTIONS.find(item => item.value === scope.row.login_type)?.label || '【异常数据】' }}
        </template>
      </el-table-column>
      <el-table-column label="试用开始时间" prop="effective_start_at" width="120">
        <template #default="scope">
          <span v-if="scope.row.effective_start_at">{{ dayjs(scope.row.effective_start_at).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试用结束时间" prop="effective_end_at" width="120">
        <template #default="scope">
          <span v-if="scope.row.effective_end_at" :class="dateHighlight(scope.row.effective_end_at)">{{ dayjs(scope.row.effective_end_at).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号状态" prop="is_enabled" width="90">
        <template #default="scope">
          <span v-if="scope.row.is_enabled">正常</span>
          <span v-else class="color-red">不可用</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="320">
        <template #default="scope">
          <div class="flex flex-wrap flex-wrap gap-8 items-center">
            <el-button :disabled="isAdmin(scope.row.account)" size="small" type="primary" @click="handleClickEdit(scope.row)">编辑</el-button>
            <el-button :disabled="isAdmin(scope.row.account)" size="small" @click="handleClickDownloadSetting(scope.row)">下载配置</el-button>
            <el-button :disabled="isAdmin(scope.row.account)" size="small" type="success" @click="handleClickAuthSetting(scope.row)">配置权限</el-button>
            <el-button :disabled="isAdmin(scope.row.account)" size="small" type="warning" @click="handleClickAuthCopy(scope.row)">复制权限</el-button>
            <el-popconfirm
              title="确认将该账号权限恢复到所有权限?"
              width="300"
              @confirm="handleClickAuthAdmin(scope.row)"
            >
              <template #reference>
                <el-button size="small" type="primary">所有权限</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              v-if="scope.row.login_type === 0"
              title="确认解绑该账号的微信登录?"
              width="300"
              @confirm="handleClickUnbindWechat(scope.row)"
            >
              <template #reference>
                <el-button :disabled="isAdmin(scope.row.account)" size="small" type="success">解绑微信</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              v-else-if="scope.row.login_type === 1"
              title="确认解绑该账号的设备登录?"
              width="300"
              @confirm="handleClickUnbindDevice(scope.row)"
            >
              <template #reference>
                <el-button :disabled="isAdmin(scope.row.account)" size="small">解绑设备</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="updateDialogVisible" title="账号信息更新" width="600">
      <AccountUpdate :account="editAccountInfo" @success="handleUpdateSuccess" />
    </el-dialog>
    <el-dialog v-model="addDialogVisible" title="账号创建" width="600">
      <AccountAdd @success="handleAddSuccess" />
    </el-dialog>
    <el-dialog
      v-model="authDialogVisible"
      align-center
      title="权限配置"
      width="90%"
    >
      <AuthSetting :account="authAccountInfo" @success="handleAuthSaveSuccess" />
    </el-dialog>
    <el-dialog v-model="copyAuthDialogVisible" title="账号权限复制" width="600">
      <AuthCopy :account="sourceAccountInfo" @success="handleAuthCopySuccess" />
    </el-dialog>
    <el-dialog v-model="updateDownloadDialogVisible" title="下载配置" width="600">
      <DownloadSetting :account="updateDownloadAccountInfo" @success="handleUpdateDownloadSuccess" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import AccountUpdate from './components/AccountUpdate.vue'
import AccountAdd from './components/AccountAdd.vue'
import AuthSetting from './components/AuthSetting.vue'
import AuthCopy from './components/AuthCopy.vue'
import DownloadSetting from './components/DownloadSetting.vue'
import dayjs from 'dayjs'
import { useFilter } from './hooks/useFilter'
import { useAccountData } from './hooks/useAccountData'
import type { AccountListItem } from './hooks/types'
import { ADMIN_AUTH_CONFIG } from '@/constants/authConfig'
import { ACCOUNT_TYPE_OPTIONS, LOGIN_TYPE_OPTIONS } from '@/constants/option'
import { useAccountStore } from '@/stores/account'
import { findNodeById } from '@/utils/data'
import { ElMessage } from 'element-plus'

const { accountList, activeName } = useFilter()
const {
  getAccountList,
  updateUserAuthConfig,
  unbindWechat,
  unbindDevice,
} = useAccountData()
const { industryOptions } = storeToRefs(useAccountStore())

const accountStatuFilter = ref<'all' | boolean>(true)
const accountTypeFilter = ref<'all' | string>('all')
const searchKeyword = ref('')
const tableData = computed(() => {
  const filteredData = accountList.value.filter((item) => {
    const matchesKeyword = item.account.toLowerCase()
      .includes(searchKeyword.value.toLowerCase())
    return matchesKeyword
  })
  if (accountStatuFilter.value === 'all' && accountTypeFilter.value === 'all') {
    return filteredData
  }
  return filteredData.filter(item => item.is_enabled === accountStatuFilter.value && (accountTypeFilter.value === 'all' ? true : item.account_type === accountTypeFilter.value))
})

async function loadAccounts() {
  const { data } = await getAccountList()
  accountList.value = data.sort((a, b) => b.user_code - a.user_code)
}
function isAdmin(account: string) {
  return account === '<EMAIL>'
}

// 编辑账号信息
const updateDialogVisible = ref(false)
const editAccountInfo = ref<AccountListItem>()
function handleClickEdit(account: AccountListItem) {
  editAccountInfo.value = account
  updateDialogVisible.value = true
}
function handleUpdateSuccess() {
  updateDialogVisible.value = false
  editAccountInfo.value = undefined
  loadAccounts()
}
// 创建账号
const addDialogVisible = ref(false)
function handleClickAdd() {
  addDialogVisible.value = true
}
function handleAddSuccess() {
  addDialogVisible.value = false
  loadAccounts()
}
// 配置账号权限
const authDialogVisible = ref(false)
const authAccountInfo = ref<AccountListItem>()
function handleClickAuthSetting(account: AccountListItem) {
  authAccountInfo.value = undefined
  nextTick(() => {
    authAccountInfo.value = account
    authDialogVisible.value = true
  })
}
function handleAuthSaveSuccess() {
  authDialogVisible.value = false
  authAccountInfo.value = undefined
}
// 配置账号权限为所有权限
async function handleClickAuthAdmin(account: AccountListItem) {
  const updateAuthConfigRes = await updateUserAuthConfig(account.user_code.toString(), ADMIN_AUTH_CONFIG)
  if (updateAuthConfigRes.error_code) {
    ElMessage.error(updateAuthConfigRes.error_msg)
    return
  }
  ElMessage.success('设置成功')
}
// 解绑账号的微信登录
async function handleClickUnbindWechat(account: AccountListItem) {
  const res = await unbindWechat(account.user_code.toString())
  if (res.error_code) {
    ElMessage.error(res.error_msg)
    return
  }
  ElMessage.success('解绑成功')
}
// 解绑账号的设备登录
async function handleClickUnbindDevice(account: AccountListItem) {
  const res = await unbindDevice(account.user_code.toString())
  if (res.error_code) {
    ElMessage.error(res.error_msg)
    return
  }
  ElMessage.success('解绑成功')
}

// 账号权限复制
const copyAuthDialogVisible = ref(false)
const sourceAccountInfo = ref<AccountListItem>()
function handleClickAuthCopy(account: AccountListItem) {
  sourceAccountInfo.value = account
  copyAuthDialogVisible.value = true
}
function handleAuthCopySuccess() {
  copyAuthDialogVisible.value = false
  sourceAccountInfo.value = undefined
}

function dateHighlight(date: string): string {
  const inputDate = dayjs(date) // 将输入日期解析为 dayjs 对象
  const today = dayjs() // 获取当前日期
  const sevenDaysAfter = today.add(7, 'day') // 计算 7 天前的日期

  if (inputDate.isBefore(sevenDaysAfter) && inputDate.isAfter(today)) {
    return 'color-orange' // 如果日期在今天往前的 7 天内
  }
  else if (inputDate.isBefore(today) || inputDate.isSame(today)) {
    return 'color-red' // 如果日期在今天之前
  }
  return '' // 其他返回空字符串
}

// 下载配置
const updateDownloadDialogVisible = ref(false)
const updateDownloadAccountInfo = ref<AccountListItem>()
function handleClickDownloadSetting(account: AccountListItem) {
  updateDownloadAccountInfo.value = account
  updateDownloadDialogVisible.value = true
}
function handleUpdateDownloadSuccess() {
  updateDownloadDialogVisible.value = false
  updateDownloadAccountInfo.value = undefined
}

async function init() {
  loadAccounts()
}

init()
</script>
