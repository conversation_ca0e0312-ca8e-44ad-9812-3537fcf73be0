export interface ImageInfo {
  original_name: string
  url: string
}
export function useUploadData() {
  async function uploadImage(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'upload_image')
    formData.append('image', file)
    const res = await talon<PERSON>pi<ImageInfo[]>(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  return {
    uploadImage,
  }
}
