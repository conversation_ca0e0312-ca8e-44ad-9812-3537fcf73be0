export const PAGE_MENU_CONFIG = [
  {
    key: 'home',
    name: '首页',
  },
  // 风口
  {
    key: 'tipping_point',
    name: '奇异风口',
    module: [
      {
        name: '风口概况',
        key: 'overview',
      },
      {
        name: '发现风口',
        key: 'discover_topics',
      },
      {
        name: '风口趋势',
        key: 'exploding_topics',
      },
      {
        name: '风口图谱',
        key: 'topic_graph',
      },
      {
        name: '发现爆品',
        key: 'discover_products',
      },
    ],
  },
  // 探测
  {
    key: 'market_sensor',
    name: '奇异探测',
    module: [
      {
        name: '市场情绪',
        key: 'market_psych',
      },
      {
        name: '市场风险',
        key: 'market_risk',
      },
      {
        name: '实时趋势',
        key: 'realtime',
      },
      {
        name: '大事件库',
        key: 'event_detection',
      },
    ],
  },
  // 社媒
  {
    key: 'social_marketing',
    name: '社媒智数',
    module: [
      {
        name: '传播分析',
        key: 'influence',
      },
      {
        name: 'VOC',
        key: 'content_analysis',
      },
      {
        name: '品牌心智',
        key: 'brand_awareness',
      },
      {
        name: '圈层透视',
        key: 'circle_perspective',
      },
      {
        name: 'K<PERSON>',
        key: 'kol',
      },
      {
        name: '热点基因',
        key: 'detonation_point',
      },
    ],
  },
  // AI
  {
    key: 'ai_product',
    name: 'AI 全息',
    module: [
      {
        name: 'AI 全生态',
        key: 'overview',
      },
      {
        name: '风口趋势',
        key: 'exploding_topics',
      },
      {
        name: '用户规模',
        key: 'user_scale',
      },
      {
        name: '用户行为',
        key: 'user_behavior',
      },
      {
        name: '用户画像',
        key: 'user_profile',
      },
      {
        name: '增长预测',
        key: 'prediction',
      },
    ],
  },
  // APP
  {
    key: 'mobile_app',
    name: 'APP 全息',
    module: [
      {
        name: 'APP 概况',
        key: 'overview',
      },
      {
        name: '用户规模',
        key: 'user_scale',
      },
      {
        name: '用户行为',
        key: 'user_behavior',
      },
      {
        name: '增长分析',
        key: 'increase_analysis',
      },
      {
        name: '终端分析',
        key: 'device_analysis',
      },
      {
        name: '用户画像',
        key: 'user_profile',
      },
    ],
  },
  // GEO
  {
    key: 'geo',
    name: 'GEO 智域',
    module: [
      {
        name: '流量趋势',
        key: 'traffic_trends',
      },
      {
        name: 'AI 知名度',
        key: 'ai_visibility',
      },
      {
        name: 'AI 情感度',
        key: 'ai_sentiment',
      },
      {
        name: '引用源',
        key: 'reference_source',
      },
      {
        name: 'Prompt',
        key: 'prompt',
      },
    ],
  },
  // 电商
  {
    key: 'ecommerce',
    name: '电商智数',
    module: [
      {
        name: '整体概况',
        key: 'overview',
      },
      {
        name: '销量分析',
        key: 'sales_analysis',
      },
      {
        name: '销售额分析',
        key: 'revenue_analysis',
      },
      {
        name: '竞争分析',
        key: 'competitive_analysis',
      },
      {
        name: '量价分析',
        key: 'aov_analysis',
      },
      {
        name: '用户旅程',
        key: 'customer_journey',
      },
      {
        name: '成交画像',
        key: 'user_profile',
      },
    ],
  },
  // 汽车
  {
    key: 'industry_automobile',
    name: '智能汽车',
    module: [
      {
        name: '汽车概况',
        key: 'overview',
      },
      {
        name: '用户分析',
        key: 'user_analysis',
      },
      {
        name: '用户口碑',
        key: 'user_reputation',
      },
      {
        name: '量价分析',
        key: 'sales_analysis',
      },
      {
        name: '用户画像',
        key: 'user_profile',
      },
    ],
  },
  // 研究中心
  {
    key: 'research_findings',
    name: '研究中心',
    module: [
      {
        name: '数据中心',
        key: 'data_center',
      },
      {
        name: '数据看板',
        key: 'dashboard',
      },
      {
        name: '公司财报',
        key: 'company_analysis',
      },
      {
        name: '图表中心',
        key: 'chart_builder',
      },
    ],
  },
]
