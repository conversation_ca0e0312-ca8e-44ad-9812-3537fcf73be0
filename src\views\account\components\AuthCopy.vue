<template>
  <div class="px-8">
    <el-form
      ref="ruleFormRef"
      label-width="auto"
      :model="ruleForm"
      :rules="rulesAuthCopy"
      status-icon
    >
      <el-form-item label="源用户编码" prop="source_code">
        <el-input v-model="ruleForm.source_code" disabled />
      </el-form-item>
      <el-form-item label="目标用户编码" prop="target_code">
        <el-input v-model="ruleForm.target_code" />
      </el-form-item>
    </el-form>
    <div class="flex gap-8">
      <div class="color-red font-bold mr-auto text-14">{{ statusMsg }}</div>
      <el-button @click="resetFormAuthCopy(ruleFormRef)">重置</el-button>
      <el-button type="primary" @click="submitFormAuthCopy(ruleFormRef)">复制</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import { useAccountData } from '../hooks/useAccountData'
import type { AccountListItem } from '../hooks/types'

const props = defineProps<{
  account?: AccountListItem
}>()
const emits = defineEmits(['success'])

const { getUserAuthConfig, updateUserAuthConfig } = useAccountData()

interface AuthCopyRuleForm {
  source_code: string
  target_code: string
}
const statusMsg = ref('')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<AuthCopyRuleForm>({
  source_code: '',
  target_code: '',
})
const rulesAuthCopy = reactive<FormRules<AuthCopyRuleForm>>({
  source_code: [
    { required: true, message: '源用户编码必填', trigger: 'blur' },
  ],
  target_code: [
    { required: true, message: '目标用户编码必填', trigger: 'blur' },
  ],
})

watch(() => props.account, () => {
  initFormValue()
  statusMsg.value = ''
}, { immediate: true })

function initFormValue() {
  if (!props.account) {
    ruleFormRef.value?.resetFields()
    statusMsg.value = ''
    return
  }
  ruleForm.source_code = props.account.user_code.toString()
}

async function submitFormAuthCopy(formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (valid) {
      const authConfigRes = await getUserAuthConfig(ruleForm.source_code)
      if (authConfigRes.error_code) {
        statusMsg.value = authConfigRes.error_msg
        return
      }
      const updateAuthConfigRes = await updateUserAuthConfig(ruleForm.target_code, authConfigRes.data)
      if (updateAuthConfigRes.error_code) {
        statusMsg.value = updateAuthConfigRes.error_msg
        return
      }
      statusMsg.value = ''
      ElMessage.success('复制成功')
      emits('success')
    }
    else {
      statusMsg.value = ''
      // console.log('error submit!', fields)
    }
  })
}
function resetFormAuthCopy(formEl: FormInstance | undefined) {
  if (!formEl) return
  statusMsg.value = ''
  formEl.resetFields()
}
</script>
