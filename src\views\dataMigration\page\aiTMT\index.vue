<template>
  <div class="flex flex-col">
    <div class="my-12">
      <el-segmented v-model="tab" :options="tabOptions" size="large" />
    </div>
    <TimeForm
      v-if="tab === '按照时间迁移'"
      class="flex-1"
      date-type-options="month"
      entity-type="AI_TMT"
    />
  </div>
</template>

<script setup lang="ts">
import TimeForm from '../aiHolo/components/TimeForm.vue'

const tab = ref('按照时间迁移')
const tabOptions = ['按照时间迁移']
</script>

<style scoped></style>
