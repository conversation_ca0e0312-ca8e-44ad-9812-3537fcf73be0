<template>
  <div>
    <el-table :data="menuTimeData">
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column :formatter="formatMenu" label="菜单" prop="menu" />
      <el-table-column label="模块 key" prop="module" />
      <el-table-column :formatter="formatModule" label="模块" prop="module" />
      <el-table-column :formatter="formatDateType" label="日期类型" prop="date_type" />
      <el-table-column label="开始时间" prop="start_time" />
      <el-table-column label="结束时间" prop="end_time" />
      <el-table-column fixed="right" label="操作">
        <template #default="scope">
          <el-button type="primary" @click="handleClickEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="isShowEditModal" title="编辑时间" width="600">
      <el-form v-if="currentEditRow" label-width="120px" :model="currentEditRow">
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="currentEditRow.start_time"
            :clearable="false"
            placeholder="选择开始时间"
            :type="currentDateType"
            value-format="YYYY-MM-DD"
            @change="handleTimeChange"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="currentEditRow.end_time"
            :clearable="false"
            placeholder="选择结束时间"
            :type="currentDateType"
            value-format="YYYY-MM-DD"
            @change="handleTimeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { DATE_TYPE_MAP, MENU_KEY_NAME_MAP, TIME_MODULE_KEY_NAME_MAP } from '@/constants/option'
import type { MenuTimeSetting } from './hooks/useData'
import useData from './hooks/useData'
import { cloneDeep } from 'lodash-es'
import { ElMessage } from 'element-plus'

const { getMenuTimeSetting, updateMenuTime } = useData()
const menuTimeData = ref<any[]>([])

function formatMenu(row: MenuTimeSetting) {
  return MENU_KEY_NAME_MAP[row.menu]
}

function formatDateType(row: MenuTimeSetting) {
  return DATE_TYPE_MAP[row.date_type.toString()]
}

function formatModule(row: MenuTimeSetting) {
  return TIME_MODULE_KEY_NAME_MAP[row.module]
}

async function loadData() {
  const { data } = await getMenuTimeSetting()
  data.sort((a, b) => {
    const onlyKey1 = a.menu + a.module + a.date_type
    const onlyKey2 = b.menu + b.module + b.date_type
    const condition1 = onlyKey1.localeCompare(onlyKey2)
    return condition1
  })
  menuTimeData.value = data
}

loadData()

const isShowEditModal = ref(false)
const currentEditRow = ref<MenuTimeSetting>()
const currentDateType = computed(() => {
  if (!currentEditRow.value) return 'date'
  const dateTypeMap = {
    0: 'date',
    1: 'week',
    2: 'month',
  } as const
  return dateTypeMap[currentEditRow.value.date_type as keyof typeof dateTypeMap]
})
function handleClickEdit(row: MenuTimeSetting) {
  isShowEditModal.value = true
  currentEditRow.value = cloneDeep(row)
}

function handleTimeChange() {
  if (!currentEditRow.value?.start_time || !currentEditRow.value?.end_time) return

  if (currentEditRow.value.start_time > currentEditRow.value.end_time) {
    const temp = currentEditRow.value.start_time
    currentEditRow.value.start_time = currentEditRow.value.end_time
    currentEditRow.value.end_time = temp

    ElMessage.info('开始时间不能大于结束时间，已自动调整')
  }
}

async function handleSave() {
  if (!currentEditRow.value) return
  const { menu, module, date_type, start_time, end_time } = currentEditRow.value
  const { error_code, error_msg } = await updateMenuTime({
    menu,
    module,
    date_type,
    start_time,
    end_time,
  })
  if (error_code === 0) {
    ElMessage.success('编辑成功')
    loadData()
    isShowEditModal.value = false
  }
  else {
    ElMessage.error(error_msg)
  }
}
</script>

<style scoped>

</style>
