import type {
  Ai<PERSON>nd<PERSON>ry,
  AiInfo,
  Task,
  TaskListParams,
  TaskStep,
  TaskStepCheckStatusData,
  TaskStepMatchData,
  WebInfo,
} from './type'
import { useFilter } from './useFilter'

export function useData() {
  const { entityType } = useFilter()

  async function getTaskList(params: TaskListParams) {
    return entityType.value === 'ai_web'
      ? getAiWebTaskList(params)
      : getAiAppTaskList(params)
  }

  async function getTaskStepList(task_id: number) {
    return entityType.value === 'ai_web'
      ? getAiWebTaskStepList(task_id)
      : getAiAppTaskStepList(task_id)
  }

  // 查询 ai_web 分拣任务列表
  async function getAiWebTaskList(params: TaskListParams) {
    const res = await talonApi<Task[]>({
      methodName: 'web_task_list',
      ...params,
    })
    return res
  }

  // 查询 ai_web 分拣任务步骤列表
  async function getAiWebTaskStepList(task_id: number) {
    const res = await talonApi<TaskStep[]>({
      methodName: 'web_task_step_list',
      task_id,
    })
    return res
  }

  // 步骤1 - 导入 ai_web 待分拣的实体文件
  async function uploadAiWebEntityFile(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'web_task_step_upload')
    formData.append('file', file)
    const res = await talonApi(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  // 步骤3 - 获取标记完的数据信息
  async function getAiWebTaskStepCheckStatusData(task_id: number) {
    const res = await talonApi<TaskStepCheckStatusData[]>({
      methodName: 'web_task_step_entity_status_check',
      task_id,
    })
    return res
  }

  // 步骤3 - 保存标记完的数据信息
  async function saveAiWebTaskStepCheckStatusData(task_id: number, data: any[]) {
    const res = await talonApi({
      methodName: 'web_task_step_entity_status_save',
      task_id,
      data,
    })
    return res
  }
  // 步骤6 - 获取需要补全的数据信息
  async function getAiWebInfo(task_id: number) {
    const res = await talonApi<WebInfo[]>({
      methodName: 'web_task_step_web_info_list',
      task_id,
    })
    return res
  }

  // 步骤6 - 更新需要补全的数据信息
  async function updateAiWebInfo(task_id: number, data: any[]) {
    const res = await talonApi({
      methodName: 'web_task_step_web_info_update',
      task_id,
      data,
    })
    return res
  }

  // 步骤7 - 获取需要映射的web列表
  async function getAiWebTaskStepMatchData(task_id: number) {
    const res = await talonApi<TaskStepMatchData[]>({
      methodName: 'web_task_step_aw_web_mapping_search',
      task_id,
    })
    return res
  }

  // 步骤7 - 更新需要映射的web列表
  async function updateAiWebTaskStepMatchData(task_id: number, data: any[]) {
    const res = await talonApi({
      methodName: 'web_task_step_aw_web_mapping_update',
      task_id,
      data,
    })
    return res
  }

  // 步骤8 - 保存最终确认的web+映射
  async function saveAiWebTaskStepFinalCheckData(
    task_id: number,
    web_info: any[],
    mapping_info: any[],
  ) {
    const res = await talonApi({
      methodName: 'web_task_step_web_info_final_check',
      task_id,
      web_info,
      mapping_info,
    })
    return res
  }

  // 查询 ai_app 分拣任务列表
  async function getAiAppTaskList(params: TaskListParams) {
    const res = await talonApi<Task[]>({
      methodName: 'include_app_task_list',
      ...params,
    })
    return res
  }

  // 查询 ai_app 分拣任务步骤列表
  async function getAiAppTaskStepList(task_id: number) {
    const res = await talonApi<TaskStep[]>({
      methodName: 'include_app_task_list',
      task_id,
    })
    return res
  }

  async function getAllAiList() {
    const res = await talonApi<AiInfo[]>({
      methodName: 'ai_app_list',
      name: '',
    })
    return res
  }

  async function getAllAiIndustryList() {
    const res = await loadV2<AiIndustry[]>({
      query: {
        methodName: 'ai_industry_list',
        limit: 300,
        order: {
          sort: 'asc',
        },
      },
    })
    return res
  }

  return {
    // common
    getTaskList,
    getTaskStepList,
    getAllAiList,
    getAllAiIndustryList,
    // ai_web
    uploadAiWebEntityFile,
    getAiWebTaskStepCheckStatusData,
    saveAiWebTaskStepCheckStatusData,
    getAiWebInfo,
    updateAiWebInfo,
    getAiWebTaskStepMatchData,
    updateAiWebTaskStepMatchData,
    saveAiWebTaskStepFinalCheckData,
  }
}
