// 用户列表
export interface AccountListItem {
  account: string;
  account_type: string;
  company_name: null;
  created_at: string;
  effective_end_at: null;
  effective_start_at: null;
  industry_code: number;
  industry_level: number;
  is_enabled: boolean;
  updated_at: string;
  user_code: number;
  user_name: string;
  login_type: number;
  login_device_id: boolean;
  wx_open_id: boolean;
}

type MenuKey = 'rosetta_x' | 'tipping_point' | 'market_sensor' |'social_marketing' | 'ai_product' |'mobile_app' | 'ecommerce' | 'industry_automobile' | 'company_analysis' | 'prediction' |'research_findings'

interface FilterWrapper<T, K> {
  white_list: K
  black_list: K
  default: T
}
// 风口概念行业
interface KeywordsCategory {
  china: FilterWrapper<string[], string[]>
  overseas: FilterWrapper<string[], string[]>
}
// 市场探测-探测对象
interface AnalysisSubject {
  filtered_words: string
  key: string
  keywords: string
  type: string
}
type AnalysisSubjectsFilter = FilterWrapper<AnalysisSubject[], AnalysisSubject[]>
// 市场探测-市场情绪-品牌行业
type BrandsIndustryFilter = FilterWrapper<string[], string[]>

// 市场探测-数据源
interface DataRepo {
  label: string
  type: string
}
interface Site {
  site_name: string
  source: string
  tags: []
  type: string
  site_id?: string
}
interface DataSource {
  data_repo: DataRepo[]
  site: Site[]
}
interface DataSourcefilter {
  china: FilterWrapper<DataSource, any>
  overseas: FilterWrapper<DataSource, any>
}

interface DataPermissionFilters {
  keywords_category?: KeywordsCategory
  analysis_subjects?: AnalysisSubjectsFilter
  data_source?: DataSourcefilter
  brands_industry?: BrandsIndustryFilter
}

interface MenuDataPermission {
  menu_time_range?: {
    start: string
    end: string
  }
  menu_entity?: any
  max_limit?: Record<string, number>
  filters?: DataPermissionFilters
  modules?: Record<string, MenuDataPermission>
}

// 用户权限配置
// export interface AccountPermissionConfig {
//   product_permissions: {
//     menu: string[] | Record<string, number>
//     download?: string[]
//   }
//   data_permissions: Partial<Record<MenuKey, MenuDataPermission>>
// }

type AuthType = 0 | 1 | 2 | 3
export interface MenuPermission {
  [key: string]: {
    auth: AuthType
    module?: {
      key: string
      auth: AuthType
    }[]
  }
}



export interface AccountPermissionConfig {
  product_permissions: {
    menu: MenuPermission
    download: string[]
  }
  data_permissions: {
    tipping_point: TippingPoint
    market_sensor: MarketSensor
    social_marketing: SocialMarketing
    ai_product: {
      menu_time_range: Partial<{
        start: string
        end: string
      }>
      menu_entity: unknown
    }
    mobile_app: {
      menu_time_range: Partial<{
        start: string
        end: string
      }>
      menu_entity: unknown
    }
    rosetta_x: {
      max_limit: Partial<Record<string, number>>
    }
    research_findings: ResearchFindings
  }
  config_permissions?: {
    menu: Record<string, number>
  }
}

interface TippingPoint {
  modules: {
    exploding_word: {
      filters: {
        keywords_category: KeywordsCategory
      }
    }
  }
}

interface MarketSensor {
  max_limit: Partial<Record<string, number>>
  filters: {
    analysis_subjects: AnalysisSubjectsFilter
  }
  modules: {
    market_psych: {
      filters: {
        brands_industry: BrandsIndustryFilter
      }
    }
    event_detection: {
      filters: {
        event_industry: FilterWrapper<string[], string[]>
      }
    }
    market_risk: {
      filters: {
        risk_brand: FilterWrapper<number, string[]>
      }
    }
    live_detection: {
      filters: {
        data_source: DataSourcefilter
      }
    }
    word_explorer: {
      filters: {
        data_source: DataSourcefilter
      }
    }
  }
}

interface SocialMarketing {
  filters: {}
  modules: {
    brand_awareness: {
      filters: {
        brand: FilterWrapper<string, string[]>
        needs_industry: FilterWrapper<string[], string[]>
      }
    }
    circle_perspective: {
      filters: {
        attention_industry: FilterWrapper<string[], string[]>
        share_brand: FilterWrapper<number, string[]>
      }
    }
    detonation_point: {
      filters: {
        activity_industry: FilterWrapper<string[], string[]>
      }
    }
    influence: {
      filters: {
        influence_industry: FilterWrapper<string[], string[]>
      }
    }
    satisfaction_analysis: {
      filters: {
        entity: FilterWrapper<number, string[]>
      }
    }
  }
}

interface ResearchFindings {
  max_limit: Partial<Record<string, number>>
  filters: {
    analysis_subjects: AnalysisSubjectsFilter
  }
  modules: {
    word_explorer: {
      filters: {
        data_source: DataSourcefilter
      }
    }
  }
}
