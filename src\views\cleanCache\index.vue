<template>
  <div class="px-16">
    <div class="font-bold text-20">实查数据缓存清理</div>
    <el-checkbox
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      size="large"
      @change="handleCheckAllChange"
    >
      全选
    </el-checkbox>
    <el-checkbox-group
      v-model="checkedModules"
      @change="handleCheckedModulesChange"
    >
      <el-checkbox v-for="module in cacheModules" :key="module.value" :label="module.value" :value="module.value">
        {{ module.label }}
      </el-checkbox>
    </el-checkbox-group>
    <el-button class="mt-16" type="primary" @click="handleCleanCache">清理缓存</el-button>
    <el-divider />

    <div class="font-bold text-20">其他缓存操作</div>
    <el-button class="mt-16" plain type="success" @click="refreshEntityCache">更新实体码表缓存（非运营后台操作更新实体码表时，需要手动触发更新缓存）</el-button>
  </div>
</template>

<script lang="ts" setup>
import { useCleanCacheData } from './hooks/useCleanCacheData'
import { useData as useStandardEntityData } from '@/views/codeTableManage/page/marketSensor/page/standardEntity/hooks/useData'

const { cleanCache } = useCleanCacheData()
const {
  updateEntityCache,
} = useStandardEntityData()

const checkAll = ref(false)
const isIndeterminate = ref(false)
const checkedModules = ref<string[]>([])
const cacheModules = [
  { label: '奇异探测-实时趋势', value: 'live_detection' },
  { label: '数据中心', value: 'data_center' },
  { label: '品牌心智-营销认知', value: 'marketing_perception' },
  { label: '品牌心智-购买决策', value: 'purchase_decision' },
  { label: '品牌心智-用户心理', value: 'customer_psych' },
  { label: 'VOC-产品讨论', value: 'product_discussion' },
  { label: 'VOC-用户评价/用户关注', value: 'content_effective_analysis' },
  { label: 'VOC-满意度分析', value: 'satisfaction_analysis' },
  { label: '奇异探测-趋势弹窗', value: 'trend_modal' },
]

function handleCheckAllChange(val: boolean) {
  checkedModules.value = val ? cacheModules.map(item => item.value) : []
  isIndeterminate.value = false
}
function handleCheckedModulesChange(value: string[]) {
  const checkedCount = value.length
  checkAll.value = checkedCount === cacheModules.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < cacheModules.length
}
async function handleCleanCache() {
  if (!checkedModules.value.length) {
    ElMessage.warning('请选择要清理的缓存模块')
    return
  }

  const res = await cleanCache(checkedModules.value)
  if (res.error_code !== 0) {
    ElMessage.error(res.error_msg)
  }
  else {
    ElMessage.success('清理成功')
  }
}

async function refreshEntityCache() {
  const { code } = await updateEntityCache()
  if (code !== 20000) {
    ElMessage.error('更新标准实体码表缓存失败，请联系管理员')
  }
}
</script>
