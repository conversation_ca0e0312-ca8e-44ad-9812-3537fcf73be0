<template>
  <div class="flex flex-1 flex-col gap-16 overflow-hidden">
    <div class="flex justify-between">
      <div class="flex gap-8 w-full">
        <el-input
          v-model="searchKeyword"
          class="flex-shrink-0"
          placeholder="请输入活动名称"
          style="width: 240px"
          :suffix-icon="Search"
          @keyup.enter="onSearch"
        />
        <el-cascader
          v-model="industryCodeList"
          clearable
          filterable
          :options="smIndustryOptions"
          placeholder="全部"
          :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'industry_code', checkStrictly: true }"
          @change="onIndustryChange"
        />
      </div>
    </div>
    <DataTable
      v-model:page-option="pageOption"
      class="flex-1"
      :columns="columns"
      :show-index-column="false"
      :status="tableStatus"
      :table-data="tableData"
      @page-change="loadData"
    />

    <!-- 编辑活动弹窗 -->
    <EditActivityModal
      v-model:visible="editModalVisible"
      :activity-data="currentEditActivity"
      @confirm="onEditConfirm"
    />
  </div>
</template>

<script setup lang="tsx">
import type { Column, PageOption } from '@/components/DataTable/DataTable.vue'
import DataTable from '@/components/DataTable/DataTable.vue'
import { ElButton, ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useAccountStore } from '@/stores/account'
import { findNodeById } from '@/utils/data'
import EditActivityModal from './EditActivityModal.vue'
import type { LibActivity } from '../hooks/useData'
import { useData } from '../hooks/useData'

const { smIndustryOptions } = storeToRefs(useAccountStore())
const {
  getLibActivityList,
  editLibActivity,
} = useData()

const searchKeyword = ref('')
const industryCodeList = ref<string[]>([])

const pageOption = ref<PageOption>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 编辑弹窗相关状态
const editModalVisible = ref(false)
const currentEditActivity = ref<any>({})

const columns: Column<LibActivity>[] = [
  {
    prop: 'activity_code',
    label: 'Code',
    minWidth: 120,
  },
  {
    prop: 'activity_name',
    label: '活动名称',
    minWidth: 200,
  },
  {
    prop: 'activity_type',
    label: '活动类型',
    minWidth: 120,
    formatter: (row) => {
      return row.activity_type || '-'
    },
  },
  {
    prop: 'related_entity',
    label: '关联对象',
    minWidth: 120,
    formatter: (row) => {
      return row.related_entity || '-'
    },
  },
  {
    prop: 'entity_logo_url',
    label: 'logo地址',
    minWidth: 150,
    customRenderer: ({ row }) => {
      if (!row.entity_logo_url) return <span>-</span>
      return (
        <img
          src={row.entity_logo_url}
          alt="logo"
          class="w-48 h-48"
        />
      )
    },
  },
  {
    prop: 'industry_code',
    label: '行业',
    minWidth: 100,
    formatter: (row) => {
      if (!row.industry_code) {
        return '-'
      }
      return findNodeById(smIndustryOptions.value, 'industry_code', row.industry_code.toString())?.industry_name || '【异常数据】'
    },
  },
  {
    prop: 'start_date',
    label: '活动开始时间',
    minWidth: 150,
  },
  {
    prop: 'end_date',
    label: '活动结束时间',
    minWidth: 150,
  },
  {
    prop: 'operation',
    label: '操作',
    minWidth: 100,
    fixed: 'right',
    customRenderer: (scope) => {
      return (
        <ElButton onClick={() => onEdit(scope.row)}>
          编辑
        </ElButton>
      )
    },
  },
]

// 模拟数据
const tableData = ref<LibActivity[]>([])
const tableStatus = ref<DataStatus>('loading')

async function loadData() {
  tableStatus.value = 'loading'
  const { data, total } = await getLibActivityList({
    offset: (pageOption.value.currentPage - 1) * pageOption.value.pageSize,
    industry_code: industryCodeList.value?.[industryCodeList.value.length - 1],
    activity_name: searchKeyword.value,
  })
  tableData.value = data
  pageOption.value.total = total || 0
  tableStatus.value = 'normal'
}
loadData()

function onSearch() {
  pageOption.value.currentPage = 1
  loadData()
}
function onIndustryChange() {
  pageOption.value.currentPage = 1
  loadData()
}

// 处理编辑按钮点击事件
function onEdit(row: LibActivity) {
  currentEditActivity.value = row
  editModalVisible.value = true
}

// 处理编辑确认事件
async function onEditConfirm(formData: Partial<LibActivity>) {
  const {
    activity_code,
    activity_name,
    activity_type,
    related_entity,
    industry_code,
  } = formData
  if (!activity_code || !activity_name || !activity_type || !related_entity || !industry_code) {
    ElMessage.error('请输入完整信息')
    return
  }
  const { data, error_code } = await editLibActivity({
    activity_code,
    activity_name,
    activity_type,
    related_entity,
    industry_code,
  })

  if (error_code === 0) {
    ElMessage.success(data)
    editModalVisible.value = false
    await loadData()
  }
  else {
    ElMessage.error(data)
  }
}
</script>
