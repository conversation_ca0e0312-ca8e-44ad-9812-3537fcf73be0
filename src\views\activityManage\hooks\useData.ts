import { pageApiV2 } from '@/apis/data'

export interface AdminActivityInfo {
  code: number
  data: AdminActivity[]
  headers: null
  message: string
  total: number
}

export interface AdminActivity {
  activity_code: string
  activity_name: string
  activity_type: null
  avg_interaction: number
  created_at: string
  dagster_run_id: string
  end_date: string
  entity_config: EntityConfig[]
  entity_logo_url: null
  interaction: number
  neg_buzz: number
  neg_rate: number
  neu_buzz: number
  nsr: number
  pos_buzz: number
  pos_rate: number
  related_entity: null
  start_date: string
  status: string
  tags: {
    buzz: number
    tag: string
  }[]
  total_buzz: number
  user_code: number
  water_rate: number
}

interface EntityConfig {
  entity_name: string
  entity_type: string
  filter_words?: string
  id: number
}

export interface LibActivity {
  activity_code: string
  start_date: string
  end_date: string
  activity_name: string
  activity_type: string
  tags: {
    tag: string
    buzz: number
  }[]
  related_entity: string
  entity_logo_url: string
  pos_buzz: number
  neu_buzz: number
  neg_buzz: number
  total_buzz: number
  interaction: number
  avg_interaction: number
  nsr: number
  pos_rate: number
  neg_rate: number
  water_rate: number
  industry_code: number
  industry_name: string
  industry_level: number
}

interface LibActivityParams {
  offset: number
  industry_code: string
  activity_name: string
}

interface EditLibActivityParams {
  activity_code: string
  activity_name: string
  activity_type: string
  related_entity: string
  industry_code: number
}
export function useData() {
  async function getAdminActivityList(offset: number) {
    const params = {
      methodName: 'custom_activity_list_by_admin',
      limit: 10,
      offset,
    }
    const res = await talonApi<AdminActivityInfo>(params)
    return res
  }

  async function getLibActivityList(params: LibActivityParams) {
    const { offset, industry_code, activity_name } = params
    const queryParams: any = {
      query: {
        methodName: 'sm_ia_hot_activity_combine',
        limit: 10,
        filters: [],
        offset,
        order: {
          'dbt.end_date': 'desc',
        },
      },
    }
    if (industry_code) {
      queryParams.query.filters.push({
        member: 'industry_code',
        operator: 'equals',
        values: [industry_code],
      })
    }
    if (activity_name) {
      queryParams.query.filters.push({
        member: 'activity_name',
        operator: 'contains',
        values: [activity_name],
      })
    }
    const res = await pageApiV2<LibActivity[]>(queryParams)
    return res
  }

  async function createActivityFromForm() {
    const params = {
      methodName: 'create_custom_activity_from_form',
    }
    const res = await talonApi(params)
    return res
  }

  async function moveActivityListToLib(activityCodeList: string[]) {
    const params = {
      methodName: 'move_custom_activity_list_to_lib',
      activity_code_list: activityCodeList,
    }
    const res = await talonApi(params)
    return res
  }

  async function editLibActivity(params: EditLibActivityParams) {
    const queryParams = {
      methodName: 'edit_lib_activity',
      ...params,
    }
    const res = await talonApi(queryParams)
    return res
  }

  return {
    getAdminActivityList,
    getLibActivityList,
    createActivityFromForm,
    moveActivityListToLib,
    editLibActivity,
  }
}
