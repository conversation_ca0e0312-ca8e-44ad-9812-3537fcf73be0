<template>
  <div class="flex flex-col gap-16 h-full w-full">
    <div class="flex items-center justify-between">
      <el-select
        v-model="industry"
        clearable
        filterable
        placeholder="请选择行业"
        style="width: 200px;"
        @change="onIndustryChange"
      >
        <el-option v-for="item in smEntityIndustryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <div class="flex gap-8">
        <el-button @click="openUploadDialog">导入</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </div>
    <Handsontable
      ref="hotTableRef"
      class="flex-1"
      :columns="columns"
      :data="tableData"
      :fixed-columns-left="3"
      :transform="transform"
      @confirm-changes="onConfirmChanges"
    />
    <!-- 导入弹窗 -->
    <UploadDialog
      v-model="uploadDialogVisible"
      :template-filename="templateFilename"
      :template-url="templateUrl"
      @upload="handleUpload"
    />
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { SmPurDecCodeTable } from './hook/useData'
import { useData } from './hook/useData'
import UploadDialog from '../../components/UploadDialog.vue'

const {
  getCodeTableList,
  updateCodeTable,
  addByXlsx,
} = useData()

const { smEntityIndustryOptions } = storeToRefs(useAccountStore())
const industry = ref(0)
const hotTableRef = shallowRef()
const uploadDialogVisible = ref(false)
const templateUrl = 'https://osscdn.datastory.com.cn/creative/static/resource/7808a29d62ef68d97d00bfcbd2bf5edf.xlsx'
const templateFilename = '购买决策因素码表上传模板.xlsx'

function initFilter() {
  industry.value = smEntityIndustryOptions.value[0].value
}
initFilter()

function onIndustryChange() {
  loadData()
}

function transform(list: SmPurDecCodeTable[]) {
  return list.map(data => ({
    ...data,
    type: data.type === '1' ? '促进' : '阻碍',
  }))
}

const columns = [
  { name: 'industry_name', label: '行业' },
  { name: 'dimension', label: '关键词大类' },
  {
    name: 'type',
    label: '分析类型',
    editor: 'select',
    selectOptions: ['促进', '阻碍'],
  },
  { name: 'keywords', label: '关键词' },
  { name: 'filter_words', label: '过滤词' },
]

const tableData = ref<SmPurDecCodeTable[]>([])
async function loadData() {
  const res = await getCodeTableList(industry.value)
  tableData.value = res.data
}
loadData()

function save() {
  hotTableRef.value?.showConfirmDialog()
}

async function onConfirmChanges(changes: any[]) {
  const { error_code, error_msg, data } = await updateCodeTable(changes)
  if (error_code === 0) {
    ElMessage.success(`保存成功,更新了${data.rows}条记录`)
  }
  else {
    ElMessage.error(error_msg)
  }
  loadData()
}

// 打开上传弹窗
function openUploadDialog() {
  uploadDialogVisible.value = true
}

// 处理上传文件
async function handleUpload(file: File) {
  try {
    const res = await addByXlsx(file)
    if (res.error_code === 0) {
      ElMessage.success('添加成功')
      uploadDialogVisible.value = false
      loadData()
    }
    else {
      ElMessage.error(res.error_msg)
    }
  }
  catch (error) {
    ElMessage.error(`上传失败：${error}`)
  }
}
</script>

<style scoped>

</style>
