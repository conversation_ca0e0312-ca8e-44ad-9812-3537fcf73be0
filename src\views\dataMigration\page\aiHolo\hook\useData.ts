interface GetAllAICodeByNameParams {
  ai_names: string[]
}

export interface AllAICodeData {
  'AI APP'?: number
  'AI WEB'?: number
  'AI Discord'?: number
  'APP+WEB合计'?: number
  ai_name: string
}

export interface CreateMigrationByAIParams {
  migration_period: number
  migration_date?: string
  migration_end_date?: string
  ai_code_list: number[]
}

export function useData() {
  async function getAllAICodeByName(params: GetAllAICodeByNameParams) {
    const res = await talonApi<AllAICodeData[]>({
      methodName: 'all_ai_code_by_name',
      ...params,
    })
    return res
  }

  async function createMigrationByAI(params: CreateMigrationByAIParams) {
    const res = await talonApi({
      methodName: 'create_migration_by_ai',
      ...params,
    })
    return res
  }

  return {
    getAllAICodeByName,
    createMigrationByAI,
  }
}
