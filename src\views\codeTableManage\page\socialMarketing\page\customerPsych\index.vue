<template>
  <div class="flex flex-col gap-16 h-full w-full">
    <div class="flex items-center justify-between">
      <SmIndustrySelector
        v-model="industryCodeList"
        @change="onIndustryChange"
      />
      <div class="flex gap-8">
        <el-button @click="openUploadDialog">导入</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </div>
    <Handsontable
      ref="hotTableRef"
      class="flex-1"
      :columns="columns"
      :data="tableData"
      :fixed-columns-left="3"
      @confirm-changes="onConfirmChanges"
    />
    <!-- 导入弹窗 -->
    <UploadDialog
      v-model="uploadDialogVisible"
      :template-filename="templateFilename"
      :template-url="templateUrl"
      @upload="handleUpload"
    />
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { CpPdCodeTable } from './hook/useData'
import { useData } from './hook/useData'
import SmIndustrySelector from '../../components/SmIndustrySelector.vue'
import UploadDialog from '../../components/UploadDialog.vue'

const {
  getCodeTableList,
  updateCodeTable,
  addByXlsx,
} = useData()

const { smIndustryOptions } = storeToRefs(useAccountStore())
const industryCodeList = ref<string[]>([])
const hotTableRef = shallowRef()
const uploadDialogVisible = ref(false)
const templateUrl = 'https://osscdn.datastory.com.cn/creative/static/resource/20bf348a8ef08f34a579ed4b5b6df948.xlsx'
const templateFilename = '用户心理码表批量上传模板.xlsx'

function initFilter() {
  industryCodeList.value = [smIndustryOptions.value[0].industry_code]
}
initFilter()

function onIndustryChange() {
  loadData()
}

const columns = [
  { name: 'industry_name', label: '行业名称' },
  { name: 'hierarchy_needs', label: '层级需求' },
  { name: 'word', label: '词条' },
  { name: 'keywords', label: '关键词' },
  { name: 'filter_words', label: '过滤词' },
]

const tableData = ref<CpPdCodeTable[]>([])
async function loadData() {
  const res = await getCodeTableList(industryCodeList.value.at(-1) || '')
  tableData.value = res.data
}
loadData()

function save() {
  hotTableRef.value?.showConfirmDialog()
}

async function onConfirmChanges(changes: any[]) {
  const { error_code, error_msg, data } = await updateCodeTable(changes)
  if (error_code === 0) {
    ElMessage.success(`保存成功,更新了${data.rows}条记录`)
  }
  else {
    ElMessage.error(error_msg)
  }
  loadData()
}

// 打开上传弹窗
function openUploadDialog() {
  uploadDialogVisible.value = true
}

// 处理上传文件
async function handleUpload(file: File) {
  try {
    const res = await addByXlsx(file)
    if (res.error_code === 0) {
      ElMessage.success('添加成功')
      uploadDialogVisible.value = false
      loadData()
    }
    else {
      ElMessage.error(res.error_msg)
    }
  }
  catch (error) {
    ElMessage.error(`上传失败：${error}`)
  }
}
</script>

<style scoped>

</style>
