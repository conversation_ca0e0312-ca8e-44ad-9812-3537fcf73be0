<template>
  <div class="py-24">
    <el-form label-width="120px" :model="formData">
      <el-form-item label="事件1 code：">
        <el-input v-model="formData.event1Code" placeholder="请输入事件1 code" />
      </el-form-item>
      <el-form-item label="事件2 code：">
        <el-input v-model="formData.event2Code" placeholder="请输入事件2 code" />
      </el-form-item>
      <el-form-item label="事件3 code：">
        <el-input v-model="formData.event3Code" placeholder="请输入事件3 code" />
      </el-form-item>
      <el-divider />
      <div class="text-end">
        <el-button
          :loading="submitStatus === 'loading'"
          type="primary"
          @click="submitForm"
        >
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { useData } from '@/views/homePage/hooks/useData'

const props = defineProps<{
  selectedCodeList: string[]
}>()

const {
  getIndustryEventCodes,
  setIndustryEventCodes,
} = useData()

// 表单数据
const formData = ref({
  event1Code: '',
  event2Code: '',
  event3Code: '',
})

const queryIndustryCode = computed(() => {
  return Number(props.selectedCodeList[props.selectedCodeList.length - 1])
})

watch(() => props.selectedCodeList, async () => {
  const { data } = await getIndustryEventCodes(queryIndustryCode.value)
  if (!data?.event_codes?.length) {
    formData.value = {
      event1Code: '',
      event2Code: '',
      event3Code: '',
    }
    return
  }
  formData.value = {
    event1Code: data.event_codes[0],
    event2Code: data.event_codes[1],
    event3Code: data.event_codes[2],
  }
}, {
  immediate: true,
})

// 提交表单
const submitStatus = ref<DataStatus>('normal')
async function submitForm() {
  submitStatus.value = 'loading'
  const res = await setIndustryEventCodes({
    industry_code: queryIndustryCode.value,
    event_codes: [
      formData.value.event1Code,
      formData.value.event2Code,
      formData.value.event3Code,
    ].filter(_ => !!_),
  })
  if (res.error_code === 0) {
    ElMessage.success('保存成功')
  }
  else {
    ElMessage.error(res.error_msg)
  }

  submitStatus.value = 'normal'
}
</script>

<style scoped>

</style>
