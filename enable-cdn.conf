server {
  listen 80;

  client_max_body_size 200m;
  client_header_buffer_size 1024k;
  large_client_header_buffers 4 1024k;

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm index.php index.jsp index.do default.do   default.jsp default.html default.php;
    try_files $uri $uri/ /index.html;
    gzip on;

    if ($request_filename ~* .*\.(?:htm|html)$) {
      add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
    }
  }
}
