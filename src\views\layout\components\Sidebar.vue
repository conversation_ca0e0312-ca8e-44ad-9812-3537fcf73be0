<template>
  <aside class="sidebar">
    <div class="logo-wrapper">
      <img
        alt="logo"
        class="logo"
        :src="isDark ? logoDark : logoLight"
      >
    </div>
    <div class="menu">
      <RouterLink
        v-for="(item, index) in visibleRoutes"
        :key="item.path"
        class="menu-item"
        :class="{ active: activeRouteName === item.name }"
        :to="item.path"
      >
        <div class="icon-wrapper">
          {{ generateRandomEmoji(index) }}
        </div>
        <span>{{ item.meta?.title }}</span>
      </RouterLink>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { commonRoutes } from '@/routers'
import { isDark } from '@/utils/dark'
import logoLight from '@/assets/logo/logo-light.svg?url'
import logoDark from '@/assets/logo/logo-dark.svg?url'

const route = useRoute()
const activeRouteName = computed(() => route.name!.toString().split('@')[0])
const { hasMenuPermission } = useAccountStore()

const visibleRoutes = computed(() => {
  return commonRoutes.filter((route) => {
    return hasMenuPermission(route.name as string)
  })
})

function generateRandomEmoji(index: number): string {
  const emojis = [
    '🍎', '❤️‍🔥', '😍', '🐔', '😼', '💯',
    '🌹', '🌈', '🎉', '🪢', '🌟', '🐜',
  ]
  return emojis[index % emojis.length]
}
</script>

<style scoped>
.sidebar {
  @apply w-280 flex-shrink-0 flex flex-col px-24 py-40 gap-40 h-full;
}
.logo-wrapper {
  @apply flex-center flex-shrink-0;
}
.dark .logo {
  filter: drop-shadow(0px 0px 10px #f24b428a);
  animation: flicker 2s linear both;
}
.logo {
  @apply w-224;
}
.menu {
  @apply flex flex-col gap-12 overflow-y-auto flex-1;
}
.menu-item {
  @apply text-black decoration-none px-6 h-56 rd-18 flex items-center gap-16 dark:text-white flex-shrink-0;
  transition: background-color 0.3s ease, color 0.3s ease;

  &.active {
    @apply bg-primary text-white  dark:text-white;
  }

  &:hover:not(.active) {
    /* @apply bg-gray-100; */
    transition: background-color 0.2s ease;
  }

  .icon-wrapper {
    @apply w-46 h-46 bg-bg-white flex-center rd-18 text-primary text-18;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  &:hover .icon-wrapper {
    transform: scale(1.05);
    box-shadow: rgba(0, 0, 0, 0.12) 0px 6px 8px -1px, rgba(0, 0, 0, 0.08) 0px 3px 5px -1px;
  }
}

@keyframes flicker{0%,100%{opacity:1}41.99%{opacity:1}42%{opacity:0}43%{opacity:0}43.01%{opacity:1}45.99%{opacity:1}46%{opacity:0}46.9%{opacity:0}46.91%{opacity:1}51.99%{opacity:1}52%{opacity:0}52.8%{opacity:0}52.81%{opacity:1}}
</style>
