<template>
  <el-dialog
    destroy-on-close
    :model-value="visible"
    title="编辑实体码表"
    width="600px"
    @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="p-16">
      <el-form label-position="left" label-width="100px" :model="form">
        <el-form-item label="实体码表ID">
          <div>{{ form.entity_code }}</div>
        </el-form-item>
        <el-form-item label="实体名称">
          <el-input v-model="form.entity_name" />
        </el-form-item>
        <el-form-item label="实体类型">
          <el-select v-model="form.entity_type_code">
            <el-option
              v-for="item in entityTypeList"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据域">
          <el-input v-model="form.data_domain" />
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="form.keywords"
            auto-size
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
          />
        </el-form-item>
        <el-form-item label="过滤词">
          <el-input
            v-model="form.filter_words"
            auto-size
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex gap-16 justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { StandardEntityCodeTable } from '../hooks/useData'

const props = defineProps<{
  visible: boolean
  entityData: StandardEntityCodeTable
  entityTypeList: OptionItem<number>[]
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', formData: Partial<StandardEntityCodeTable>): void
}>()

const form = ref<Partial<StandardEntityCodeTable>>({
  entity_code: 0,
  entity_name: '',
  entity_type_code: 0,
  data_domain: '',
  keywords: '',
  filter_words: '',
})

// 监听 activityData 变化，更新表单数据
watch(() => props.visible, () => {
  if (props.visible) {
    form.value = cloneDeep({
      ...props.entityData,
    })
  }
}, { immediate: true, deep: true })

function handleClose() {
  emit('update:visible', false)
}

function handleConfirm() {
  emit('confirm', cloneDeep({
    ...form.value,
  }))
}
</script>

<style scoped>
.p-16 {
  padding: 16px;
}
.border-b {
  border-bottom: 1px solid #ebeef5;
}
</style>
