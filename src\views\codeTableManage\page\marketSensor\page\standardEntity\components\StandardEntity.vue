<template>
  <div class="flex flex-1 flex-col gap-16">
    <div class="flex gap-16">
      <el-input
        v-model="searchKeyword"
        class="mr-auto"
        placeholder="请输入实体名称"
        :prefix-icon="Search"
        style="width: 240px;"
        @keyup.enter="onSearch"
      />
      <el-link :icon="Download" type="primary" :underline="false" @click="downloadTemplate">下载模板</el-link>
      <el-upload
        accept=".xlsx"
        :disabled="uploadLoading"
        :http-request="onBatchUpload"
        :show-file-list="false"
      >
        <el-button :loading="uploadLoading">导入新码表</el-button>
      </el-upload>
    </div>
    <!-- 没有搜索关键词时显示提示 -->
    <div v-if="!searchKeyword.trim()" class="tip-container">
      请输入实体名称进行搜索
    </div>
    <!-- 有搜索关键词但无结果时显示提示 -->
    <div v-else-if="searchKeyword.trim() && tableStatus === 'empty'" class="tip-container">
      未找到相关记录
    </div>
    <DataTable
      v-else-if="(tableStatus === 'normal' && tableData.length > 0) || tableStatus === 'loading'"
      class="flex-1"
      :columns="columns"
      :show-index-column="false"
      :status="tableStatus"
      :table-data="tableData"
    />

    <!-- 编辑实体弹窗 -->
    <EditStandardEntityModal
      v-model:visible="editModalVisible"
      :entity-data="currentEditEntity"
      :entity-type-list="entityTypeList"
      @confirm="onEditConfirm"
    />
  </div>
</template>

<script setup lang="tsx">
import type { UploadRequestOptions } from 'element-plus'
import { ElButton, ElPopconfirm } from 'element-plus'
import type { Column } from '@/components/DataTable/DataTable.vue'
import type { StandardEntityCodeTable } from '../hooks/useData'
import { Download, Search } from '@element-plus/icons-vue'
import { useData } from '../hooks/useData'
import EditStandardEntityModal from './EditStandardEntityModal.vue'

const {
  getStandardEntityList,
  editStandardEntity,
  deleteStandardEntity,
  uploadStandardEntity,
  getEntityTypeList,
  updateEntityCache,
} = useData()

const entityTypeList = ref<OptionItem<number>[]>([])
const searchKeyword = ref('')

const tableStatus = ref<DataStatus>('normal')
const tableData = ref<StandardEntityCodeTable[]>([])

// 编辑弹窗相关状态
const editModalVisible = ref(false)
const currentEditEntity = ref<any>({})

const columns: Column<StandardEntityCodeTable>[] = [
  {
    prop: 'entity_code',
    label: '实体码表ID',
    width: 110,
  },
  {
    prop: 'entity_name',
    label: '实体名称',
    minWidth: 120,
  },
  {
    prop: 'entity_type_code',
    label: '实体类型',
    formatter: (row) => {
      const type = entityTypeList.value.find(item => item.value === row.entity_type_code)
      return type?.label || '-'
    },
  },
  {
    prop: 'data_domain',
    label: '数据域',
  },
  {
    prop: 'keywords',
    label: '关键词',
    minWidth: 200,
    showOverflowTooltip: true,
  },
  {
    prop: 'filter_words',
    label: '过滤词',
    minWidth: 160,
    showOverflowTooltip: true,
  },
  {
    prop: 'updated_at',
    label: '最新操作时间',
    width: 180,
  },
  {
    prop: 'updated_by',
    label: '最新操作人',
  },
  {
    prop: 'operation',
    label: '操作',
    minWidth: 100,
    fixed: 'right',
    customRenderer: (scope) => {
      return (
        <div class="flex">
          <ElButton type='primary' size='small' text onClick={() => onEdit(scope.row)}>编辑</ElButton>
          <ElPopconfirm
            title="确认删除该记录吗？"
            width="200"
            onConfirm={() => onDelete(scope.row)}
            v-slots={{
              reference: () => (
                <ElButton type='info' size='small' text>删除</ElButton>
              ),
            }}
          >
          </ElPopconfirm>
        </div>
      )
    },
  },
]

watch(searchKeyword, (newVal) => {
  if (!newVal) {
    loadData()
  }
})

// 加载数据
async function loadData() {
  if (!searchKeyword.value.trim()) {
    tableStatus.value = 'normal'
    tableData.value = []
    return
  }
  tableStatus.value = 'loading'
  const { data } = await getStandardEntityList(searchKeyword.value.trim())
  tableData.value = data
  tableStatus.value = data.length ? 'normal' : 'empty'
}

function onSearch() {
  loadData()
}

const uploadLoading = ref(false)
async function onBatchUpload(options: UploadRequestOptions) {
  try {
    uploadLoading.value = true
    const { error_code, error_msg } = await uploadStandardEntity(options.file)
    if (error_code !== 0) {
      ElMessage.error(error_msg)
      return
    }
    ElMessage.success('导入成功')
    loadData()
    refreshEntityCache()
  }
  catch (e) {
    ElMessage.error('上传失败')
  }
  finally {
    uploadLoading.value = false
  }
}

// 处理编辑按钮点击事件
function onEdit(row: StandardEntityCodeTable) {
  currentEditEntity.value = row
  editModalVisible.value = true
}

// 处理删除按钮点击事件
async function onDelete(row: StandardEntityCodeTable) {
  const { error_code, error_msg } = await deleteStandardEntity(row.entity_code)
  if (error_code === 0) {
    ElMessage.success('删除成功')
    await loadData()
    refreshEntityCache()
  }
  else {
    ElMessage.error(`删除失败，原因：${error_msg}`)
  }
}

// 处理编辑确认事件
async function onEditConfirm(formData: Partial<StandardEntityCodeTable>) {
  const { entity_code, entity_name, entity_type_code, data_domain, keywords, filter_words } = formData
  if (!entity_code || !entity_name?.trim() || !entity_type_code || !keywords?.trim()) {
    ElMessage.error('请输入完整信息')
    return
  }
  const { error_msg, error_code } = await editStandardEntity({
    entity_code,
    entity_name: entity_name?.trim() || '',
    entity_type_code,
    data_domain: data_domain?.trim() || '',
    keywords: keywords?.trim() || '',
    filter_words: filter_words?.trim() || '',
  })
  if (error_code === 0) {
    ElMessage.success('编辑成功')
    editModalVisible.value = false
    loadData()
    refreshEntityCache()
  }
  else {
    ElMessage.error(`编辑失败，原因：${error_msg}`)
  }
}

// 下载模板
async function downloadTemplate() {
  const url = 'https://osscdn.datastory.com.cn/creative/static/resource/2be7883d6b7e2f925445e0f159f9808b.xlsx'
  const filename = '实体码表批量上传模板.xlsx'

  try {
    const response = await fetch(url)
    const blob = await response.blob()

    const objectUrl = window.URL.createObjectURL(new Blob([blob], {
      type: 'application/octet-stream',
    }))

    const link = document.createElement('a')
    link.href = objectUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(objectUrl)
  }
  catch (error) {
    console.error('下载模板失败:', error)
  }
}

async function loadEntityTypeList() {
  const { data } = await getEntityTypeList()
  entityTypeList.value = data.map(item => ({
    label: item.entity_type_cn,
    value: item.entity_type_code,
  }))
}

async function refreshEntityCache() {
  const { code } = await updateEntityCache()
  if (code !== 20000) {
    ElMessage.error('更新标准实体码表缓存失败，请联系管理员')
  }
}

onMounted(async () => {
  // 加载实体类型列表
  await loadEntityTypeList()
})
</script>

<style lang="scss" scoped>
.tip-container {
  @apply flex items-center justify-center h-400;
}
</style>
