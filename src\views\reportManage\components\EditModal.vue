<template>
  <el-dialog
    v-model="visible"
    destroy-on-close
    title="编辑报告"
    width="600px"
  >
    <el-form
      ref="formRef"
      label-width="100px"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="报告标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入报告标题" />
      </el-form-item>

      <el-form-item label="所属行业" prop="industry_code">
        <el-select v-model="formData.industry_code" class="w-full" placeholder="请选择所属行业">
          <el-option
            v-for="item in industryOptions"
            :key="item.mp_industry_code"
            :label="item.industry_name"
            :value="item.mp_industry_code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="报告简介" prop="description">
        <el-input
          v-model="formData.description"
          placeholder="请输入报告简介"
          :rows="3"
          type="textarea"
        />
      </el-form-item>

      <el-form-item label="封面图URL" prop="coverImage">
        <el-input v-model="formData.coverImage" placeholder="请输入封面图片URL地址" />
      </el-form-item>

      <el-form-item label="主图URL" prop="mainImage">
        <el-input v-model="formData.mainImage" placeholder="请输入主图片URL地址" />
      </el-form-item>

      <el-form-item label="PDF URL" prop="pdfUrl">
        <el-input v-model="formData.pdfUrl" placeholder="请输入PDF文件URL地址" />
      </el-form-item>

      <el-form-item label="报告时间" prop="reportTime">
        <el-date-picker
          v-model="formData.reportTime"
          class="w-full"
          placeholder="请选择报告时间"
          type="datetime"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="formData.status"
          :active-text="formData.status === 1 ? '启用' : '停用'"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { ReportItem } from '../hooks/useData'

const emit = defineEmits(['success'])
const { industryOptions } = storeToRefs(useAccountStore())

const visible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

const formData = ref<ReportItem>({
  title: '',
  description: '',
  coverImage: '',
  mainImage: '',
  pdfUrl: '',
  industry_code: '',
  reportTime: '',
  status: 1,
  operateTime: '',
  operator: '',
})

const rules: FormRules = {
  title: [{ required: true, message: '请输入报告标题' }],
  description: [{ required: true, message: '请输入报告简介' }],
  coverImage: [{ required: true, message: '请输入封面图片URL' }],
  mainImage: [{ required: true, message: '请输入主图片URL' }],
  industry_code: [{ required: true, message: '请选择所属行业' }],
  reportTime: [{ required: true, message: '请选择报告时间' }],
  pdfUrl: [{ required: true, message: '请输入PDF文件URL' }],
}

async function handleSubmit() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 这里调用更新API
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('更新成功')
    emit('success')
    visible.value = false
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

function resetForm() {
  formRef.value?.resetFields()
}

// 监听弹窗关闭
watch(visible, (val) => {
  if (!val) {
    resetForm()
  }
})

// 暴露方法给父组件调用
defineExpose({
  show: (data: ReportItem) => {
    formData.value = { ...data }
    visible.value = true
  },
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}
</style>
