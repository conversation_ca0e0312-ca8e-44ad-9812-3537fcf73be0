# build stage
FROM node:18-alpine as build-stage
WORKDIR /app
RUN npm install -g pnpm
COPY package.json pnpm-lock.yaml .npmrc ./
RUN pnpm install
COPY . .
RUN pnpm build

# production stage
FROM nginx:stable-alpine as production-stage

ENV LANG en_US.UTF-8
ENV LANGUAGE en_US:en
ENV TZ Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
USER root

RUN mkdir /workspace
WORKDIR /workspace

COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY --from=build-stage /app/run.sh /app/*.conf /workspace/

EXPOSE 80

ENTRYPOINT ["/bin/sh", "/workspace/run.sh"]
