export interface CreateMigrationByAPPParams {
  migration_period: number
  migration_date?: string
  migration_end_date?: string
  app_names: string[]
}

export function useData() {
  async function createMigrationByAPP(params: CreateMigrationByAPPParams) {
    const res = await talon<PERSON><PERSON>({
      methodName: 'create_migration_by_app',
      ...params,
    })
    return res
  }

  return {
    createMigrationByAPP,
  }
}
