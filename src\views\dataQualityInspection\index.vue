<template>
  <div class="px-16">
    <div class="my-12">
      <el-segmented v-model="tab" :options="tabOptions" size="large" />
    </div>
    <InspectionStatistics v-if="tab === '质检统计'" />
    <RulesDescription v-if="tab === '规则说明'" />
  </div>
</template>

<script setup lang="ts">
import InspectionStatistics from './page/InspectionStatistics/index.vue'
import RulesDescription from './page/RulesDescription/index.vue'

const tab = ref('质检统计')
const tabOptions = ['质检统计', '规则说明']
</script>

<style scoped>

</style>
