<template>
  <div>
    <el-table :data="tableData">
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column label="任务名称" prop="jobNameCn">
        <template #default="scope">
          <div class="flex gap-4 items-center">
            <span>{{ scope.row.jobNameCn }}</span>
            <el-link :href="scope.row.feishuLink" :icon="Memo" target="_blank" type="warning" />
            <el-tooltip v-if="scope.row.tip" :content="scope.row.tip" placement="top">
              <Warning class="h-16 w-16" />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="最新任务状态" prop="status">
        <template #default="scope">
          <el-tag v-if="!scope.row.status" effect="plain" size="small" type="info">未执行过</el-tag>
          <el-tag v-else-if="scope.row.status === 'SUCCESS'" effect="plain" size="small" type="success">已完成</el-tag>
          <el-tag v-else-if="['FAILURE', 'CANCELED'].includes(scope.row.status)" effect="plain" size="small" type="danger">运行失败</el-tag>
          <el-tag v-else effect="plain" size="small" type="warning">进行中</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最新任务开始时间" prop="startTime" width="200">
        <template #default="scope">
          {{ scope.row.startTime ? scope.row.startTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="最新任务结束时间" prop="endTime" width="200">
        <template #default="scope">
          {{ scope.row.endTime ? scope.row.endTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-popconfirm
            title="请确保飞书表已填写完成，确认现在触发运行任务吗?"
            width="300"
            @confirm="onTriggerRun(scope.row)"
          >
            <template #reference>
              <el-button :disabled="scope.row.status && !finishStatus.includes(scope.row.status)" :loading="scope.row.buttonLoading" size="small" type="primary">运行任务</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { Memo, Warning } from '@element-plus/icons-vue'
import { useData } from './hooks/useData'
import type { JobStatus } from './hooks/type'
import { ElMessage } from 'element-plus'

interface TableDataItem {
  feishuLink: string
  jobName: string
  jobNameCn: string
  buttonLoading: boolean
  status?: JobStatus
  startTime?: string
  endTime?: string
  runId?: string
  tip?: string
}

const {
  getJobRunInfoByJobName,
  triggerJobRun,
} = useData()

const tableData = ref<TableDataItem[]>([
  {
    jobName: 'score_codetable_job',
    jobNameCn: '码表打分',
    buttonLoading: false,
    feishuLink: 'https://xsignal-ai.feishu.cn/wiki/KrevwD7uHijJoOkuT6pcJlLdnt8?sheet=e4lBbO',
  },
  {
    jobName: 'order_cut_codetable_job',
    jobNameCn: '码表排序截断',
    buttonLoading: false,
    feishuLink: 'https://xsignal-ai.feishu.cn/wiki/KrevwD7uHijJoOkuT6pcJlLdnt8?sheet=VATBzO',
  },
  {
    jobName: 'llm_codetable_job',
    jobNameCn: 'AI 生成 - 品牌码表',
    buttonLoading: false,
    feishuLink: 'https://xsignal-ai.feishu.cn/wiki/KrevwD7uHijJoOkuT6pcJlLdnt8?sheet=wfaatW',
  },
  {
    jobName: 'llm_codetable_exploding_word_job',
    jobNameCn: 'AI 生成 - 风口概念词码表',
    buttonLoading: false,
    feishuLink: 'https://xsignal-ai.feishu.cn/wiki/KrevwD7uHijJoOkuT6pcJlLdnt8?sheet=Bmntgq',
    tip: '请确保所有行的(赛道大类/细分赛道)相同',
  },
])
const finishStatus = ['SUCCESS', 'FAILURE', 'CANCELED']

async function onTriggerRun(row: TableDataItem) {
  row.buttonLoading = true
  const res = await triggerJobRun(row.jobName)
  if (res.error_code === 0 && res.data?.run_id) {
    row.status = 'QUEUED'
    row.runId = res.data.run_id
    row.startTime = ''
    row.endTime = ''
  }
  else {
    ElMessage.error('触发任务失败，请联系管理员')
  }
  row.buttonLoading = false
  // 10 秒后刷新数据
  setTimeout(() => {
    loadData()
  }, 10000)
}

async function loadData() {
  for (let i = 0; i < tableData.value.length; i++) {
    const row = tableData.value[i]
    // 已经结束的状态不再请求
    if (finishStatus.includes(row.status!))
      continue
    const res = await getJobRunInfoByJobName(row.jobName)
    if (res.error_code === 0 && res.data) {
      row.status = res.data.status
      // 接口返回的时间格式是时间戳，单位 秒，转换成字符串
      res.data.startTime && (row.startTime = new Date(res.data.startTime * 1000).toLocaleString())
      res.data.endTime && (row.endTime = new Date(res.data.endTime * 1000).toLocaleString())
      row.runId = res.data.runId
    }
  }
}
// 定时刷新数据
const timer = ref<NodeJS.Timer>()

async function init() {
  await loadData()

  // 定时刷新数据，2 分钟一次
  timer.value = setInterval(() => {
    loadData()
  }, 2 * 60 * 1000)
}

onMounted(() => {
  init()
})
onUnmounted(() => {
  clearInterval(timer.value)
})
</script>
