<template>
  <div class="layout-wrapper">
    <Sidebar />
    <div class="content-container">
      <Header />
      <main>
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import Sidebar from './components/Sidebar.vue'
import Header from './components/Header.vue'
</script>

<style scoped>
.dark .layout-wrapper {
  background-image: linear-gradient(184deg, #1e1e1e 0%,#1e1e1e 95%, #2f1818 100%);
}
.layout-wrapper {
  @apply w-full h-full flex bg-gray-50;
  background-image: linear-gradient(180deg, #f9fafb 0%,#f9fafb 70%, #fffbfb 100%);
}
.content-container {
  @apply flex-1 h-full overflow-hidden flex flex-col px-24 pb-24 box-border gap-32;
}
main {
  @apply w-full h-full overflow-auto flex-1 p-24 box-border b-rd-12 bg-bg-white dark:bg-bg-black;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 20px;
}
</style>
