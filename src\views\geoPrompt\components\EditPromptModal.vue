<template>
  <el-dialog
    destroy-on-close
    :model-value="visible"
    title="编辑Prompt"
    width="600px"
    @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="p-16">
      <el-form label-position="left" label-width="100px" :model="form">
        <el-form-item label="Question ID">
          <div>{{ form.question_id }}</div>
        </el-form-item>
        <el-form-item label="Prompt">
          <div>{{ form.prompt }}</div>
        </el-form-item>
        <el-form-item label="所属行业">
          <el-cascader
            v-model="industryCodeList"
            filterable
            :options="industryOptions"
            :props="{
              expandTrigger: 'hover',
              label: 'industry_name',
              value: 'industry_code',
              checkStrictly: true,
            }"
            @change="onIndustryChange"
          />
        </el-form-item>
        <el-form-item label="采集状态">
          <el-radio-group v-model="form.collect_status" size="default">
            <el-radio-button label="不采集" :value="0" />
            <el-radio-button label="常规采集" :value="1" />
            <el-radio-button label="优先采集" :value="2" />
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex gap-16 justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { IndustryOptions, Prompt } from '../hooks/useData'

const props = defineProps<{
  visible: boolean
  data?: Prompt
  industryOptions: IndustryOptions[]
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', formData: Partial<Prompt>): void
}>()

const form = ref<Partial<Prompt>>({
  question_id: 0,
  prompt: '',
  industry_code: 0,
  collect_status: 0,
})
const industryCodeList = ref<string[]>([])
function onIndustryChange(value: string[]) {
  form.value.industry_code = Number(value.at(-1))
}

// 监听 activityData 变化，更新表单数据
watch(() => props.visible, () => {
  if (props.visible) {
    form.value = cloneDeep({
      ...props.data,
    })
    // 初始化行业取值
    industryCodeList.value = []
    props.industryOptions.forEach((item) => {
      const child = item.children?.find(child => child.industry_code === props.data?.industry_code?.toString())
      if (child) {
        industryCodeList.value = [item.industry_code, child.industry_code]
      }
    })
  }
}, { immediate: true, deep: true })

function handleClose() {
  emit('update:visible', false)
}

function handleConfirm() {
  emit('confirm', cloneDeep({
    ...form.value,
  }))
}
</script>

<style scoped>
.p-16 {
  padding: 16px;
}
.border-b {
  border-bottom: 1px solid #ebeef5;
}
</style>
