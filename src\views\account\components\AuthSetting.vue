<template>
  <div class="auth-setting px-16">
    <div class="flex-1 overflow-auto">
      <div class="flex gap-36 items-center mb-16 mr-16 w-200">
        <span class="text-14 whitespace-nowrap">用户编码：{{ account?.user_code }}</span>
        <span class="text-14 whitespace-nowrap">用户名称：{{ account?.user_name }}</span>
        <span class="text-14 whitespace-nowrap">用户邮箱：{{ account?.account }}</span>
      </div>
      <el-collapse v-model="activeBlock">
        <el-collapse-item name="menuPermission" title="菜单权限">
          <div class="flex gap-8 mb-16">
            <el-button plain size="small" type="primary" @click="updateMenuPermission(1)">全选 正常</el-button>
            <el-button plain size="small" type="warning" @click="updateMenuPermission(3)">全选 锁住</el-button>
            <el-button plain size="small" type="info" @click="updateMenuPermission(0)">全选 隐藏</el-button>
          </div>
          <div class="grid grid-cols-3 grid-gap-8">
            <div v-for="menu in menuList" :key="menu.value" class="mb-16">
              <div class="flex flex-nowrap gap-16">
                <div class="font-bold text-right w-60 whitespace-nowrap">{{ menu.label }}</div>
                <el-radio-group v-model="menu.permission" class="flex-nowrap!" :disabled="menu.disabled" size="small">
                  <el-radio-button label="正常" :value="1" />
                  <el-radio-button label="锁住" :value="3" />
                  <el-radio-button label="隐藏" :value="0" />
                </el-radio-group>
              </div>
              <!-- 显示菜单下的模块权限 -->
              <div v-if="menu.modules && menu.modules.length > 0" class="mt-8">
                <div v-for="module in menu.modules" :key="module.key" class="flex flex-nowrap gap-16 mb-4">
                  <div class="text-right w-60 whitespace-nowrap">{{ `- ${module.label}` }}</div>
                  <el-radio-group v-model="module.permission" class="flex-nowrap!" :disabled="menu.disabled || menu.permission === 0" size="small">
                    <el-radio-button label="正常" :value="1" />
                    <el-radio-button label="锁住" :value="3" />
                    <el-radio-button label="隐藏" :value="0" />
                  </el-radio-group>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_tipping_point" title="个性化配置 - 奇异风口">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>风口概念 默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="text-14 whitespace-nowrap">中国</span>
                  <!-- <el-input
                  v-model="explodeCnDefault"
                  class="w-100"
                  placeholder="中国行业"
                /> -->
                  <el-select
                    v-model="explodeCnDefault"
                    class="w-100"
                    placeholder="中国行业"
                  >
                    <el-option
                      v-for="item in explodeCategoryLv1OptionsCN"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="flex gap-8 items-center">
                  <span class="text-14 whitespace-nowrap">海外</span>
                  <!-- <el-input
                  v-model="explodeUsDefault"
                  class="w-100"
                  placeholder="海外行业"
                /> -->
                  <el-select
                    v-model="explodeUsDefault"
                    class="w-100"
                    placeholder="海外行业"
                  >
                    <el-option
                      v-for="item in explodeCategoryLv1OptionsUS"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </template>
            </el-card>
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_market_sensor" title="个性化配置 - 市场探测">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex items-center justify-between">
                  <span>次数限制</span>
                  <el-switch
                    v-model="msEnabledCnt"
                    active-text="限制"
                    inactive-text="不限制"
                    size="small"
                  />
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="text-14 whitespace-nowrap">实时探测</span>
                  <el-input
                    v-model="msRealtimeCnt"
                    class="w-100"
                    placeholder="输入次数限制"
                    type="number"
                  />
                </div>
                <div class="flex gap-8 items-center">
                  <span class="text-14 whitespace-nowrap">即时调研</span>
                  <el-input
                    v-model="msWordExplorerCnt"
                    class="w-100"
                    placeholder="输入次数限制"
                    type="number"
                  />
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>探测对象(实时探测)</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">关键词</span>
                  <el-input
                    v-model="msFilterKeywords"
                    class="w-100"
                    placeholder="配置关键词"
                  />
                </div>
                <div class="flex gap-8 items-center">
                  <span class="text-14 whitespace-nowrap">实体类型</span>
                  <el-select
                    v-model="msEntityType"
                    class="w-100"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in ENTITY_TYPE_OPTIONS"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>市场情绪 - 品牌默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">行业</span>
                  <el-cascader
                    v-model="msBrandIndustry"
                    clearable
                    filterable
                    :options="brandIndustryOptions"
                    :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'mp_industry_code', checkStrictly: true }"
                  />
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>市场风险 - 默认品牌</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">品牌</span>
                  <el-select
                    v-model="msRiskBrand"
                    class="w-100"
                    clearable
                    filterable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in msRiskBrandOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>大事件库 - 默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">行业</span>
                  <el-cascader
                    v-model="msEventIndustry"
                    clearable
                    filterable
                    :options="msEventIndustryOptions"
                    :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'mp_industry_code', checkStrictly: true }"
                  />
                </div>
              </template>
            </el-card>
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_social_marketing" title="个性化配置 - 社媒智数">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>传播分析 - 品牌影响力默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">行业</span>
                  <el-cascader
                    v-model="smInfluenceIndustry"
                    clearable
                    filterable
                    :options="smIndustryOptions"
                    :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'industry_code', checkStrictly: true }"
                  />
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>品牌心智 - 需求认知默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">行业</span>
                  <el-cascader
                    v-model="smNeedsIndustry"
                    clearable
                    filterable
                    :options="smIndustryOptions"
                    :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'industry_code' }"
                  />
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>圈层透视 - 品类关注度默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">行业</span>
                  <el-cascader
                    v-model="smAttentionIndustry"
                    clearable
                    filterable
                    :options="smIndustryOptions"
                    :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'industry_code' }"
                  />
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>引爆点 - 热门活动默认行业</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">行业</span>
                  <el-cascader
                    v-model="smActivityIndustry"
                    clearable
                    filterable
                    :options="smActivityIndustryOptions"
                    :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'industry_code', checkStrictly: true }"
                  />
                </div>
              </template>
            </el-card>
          </div>
          <div class="flex gap-16 mt-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>受众/品牌心智/内容引擎/圈层 - 默认品牌</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">品牌</span>
                  <el-select
                    v-model="smBrand"
                    class="w-100"
                    clearable
                    filterable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in smBrandOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label"
                    />
                  </el-select>
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>满意度分析 - 默认品牌</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">品牌</span>
                  <el-select
                    v-model="smSaEntity"
                    class="w-100"
                    clearable
                    filterable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in smSaEntityOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </template>
            </el-card>
          <!-- <el-card class="auth-setting-card">
            <template #header>
              <div class="flex h-24 items-center justify-between">
                <span>圈层透视 - 品牌占有率默认品牌</span>
              </div>
            </template>
            <template #default>
              <div class="flex gap-8 items-center mb-8">
                <span class="inline-block text-14 whitespace-nowrap">品牌</span>
                <el-select
                  v-model="smShareBrand"
                  class="w-100"
                  filterable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in msRiskBrandOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </template>
          </el-card> -->
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_research_findings" title="个性化配置 - 研究中心">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex items-center justify-between">
                  <span>次数限制</span>
                  <el-switch
                    v-model="rfEnabledCnt"
                    active-text="限制"
                    inactive-text="不限制"
                    size="small"
                  />
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center">
                  <span class="text-14 whitespace-nowrap">即时调研</span>
                  <el-input
                    v-model="rfWordExplorerCnt"
                    class="w-100"
                    placeholder="输入次数限制"
                    type="number"
                  />
                </div>
              </template>
            </el-card>
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>探测对象(即时调研)</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="inline-block text-14 whitespace-nowrap">关键词</span>
                  <el-input
                    v-model="rfFilterKeywords"
                    class="w-100"
                    placeholder="配置关键词"
                  />
                </div>
                <div class="flex gap-8 items-center">
                  <span class="text-14 whitespace-nowrap">实体类型</span>
                  <el-select
                    v-model="rfEntityType"
                    class="w-100"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in ENTITY_TYPE_OPTIONS"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </template>
            </el-card>
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_ai_product" title="个性化配置 - AI 全息">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>时间权限</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <el-date-picker
                    v-model="aiTimeRange"
                    end-placeholder="结束月份"
                    range-separator="To"
                    start-placeholder="开始月份"
                    type="monthrange"
                  />
                </div>
              </template>
            </el-card>
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_mobile_app" title="个性化配置 - APP 全息">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex h-24 items-center justify-between">
                  <span>时间权限</span>
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <el-date-picker
                    v-model="appTimeRange"
                    end-placeholder="结束月份"
                    range-separator="To"
                    start-placeholder="开始月份"
                    type="monthrange"
                  />
                </div>
              </template>
            </el-card>
          </div>
        </el-collapse-item>
        <el-collapse-item name="dataPermission_rosetta_x" title="个性化配置 - Rosetta X">
          <div class="flex gap-16">
            <el-card class="auth-setting-card">
              <template #header>
                <div class="flex items-center justify-between">
                  <span>次数限制</span>
                  <el-switch
                    v-model="rsEnabledCnt"
                    active-text="限制"
                    inactive-text="不限制"
                    size="small"
                  />
                </div>
              </template>
              <template #default>
                <div class="flex gap-8 items-center mb-8">
                  <span class="text-14 whitespace-nowrap">AI 实时探测</span>
                  <el-input
                    v-model="rsRealtimeCnt"
                    class="w-100"
                    placeholder="输入次数限制"
                    type="number"
                  />
                </div>
                <div class="flex gap-8 items-center mb-8">
                  <span class="text-14 whitespace-nowrap">DataGPT</span>
                  <el-input
                    v-model="rsDatagptCnt"
                    class="w-100"
                    placeholder="输入次数限制"
                    type="number"
                  />
                </div>
                <div class="flex gap-8 items-center">
                  <span class="text-14 whitespace-nowrap">AI 报告</span>
                  <el-input
                    v-model="rsAireportCnt"
                    class="w-100"
                    placeholder="输入次数限制"
                    type="number"
                  />
                </div>
              </template>
            </el-card>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="flex justify-end mb-16 mt-16">
      <el-button type="primary" @click="generateConfig">生成配置</el-button>
    </div>
    <div class="color-red h-30 pl-32 text-14 text-right">{{ statusMsg }}</div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { useAccountData } from '../hooks/useAccountData'
import type { AccountListItem, AccountPermissionConfig, AuthType, DataPermissionFilters, MenuPermission } from '../hooks/types'
import { cloneDeep, isEmpty, merge } from 'lodash-es'
import { DEFAULT_AUTH_PERMISSIONS } from '@/constants/authConfig'
import { ENTITY_TYPE_OPTIONS } from '@/constants/option'
import { ElMessage } from 'element-plus'
import { PAGE_MENU_CONFIG } from '@/constants/system'

const props = defineProps<{
  account?: AccountListItem
}>()
const emits = defineEmits(['success'])

const {
  updateUserAuthConfig, getUserAuthConfig,
  getBrandsIndustry, getExplodingTopicsCategoryLv1,
  getRiskBrand, getSMIndustry, getSMBrand,
  getSMSaEntity,
} = useAccountData()

const statusMsg = ref('')
const targetUserCode = ref('')
watch(() => props.account, () => {
  if (props.account) {
    targetUserCode.value = props.account.user_code.toString()
    loadUserCurrentAuthConfig()
  }
}, { immediate: true })

const activeBlock = ref<string[]>([
  'menuPermission',
  'dataPermission_tipping_point',
  'dataPermission_market_sensor',
  'dataPermission_social_marketing',
  'dataPermission_research_findings',
  'dataPermission_ai_product',
  'dataPermission_mobile_app',
  'dataPermission_rosetta_x',
])
// 菜单配置
interface MenuAuthConfig {
  label: string
  value: string
  permission: AuthType
  disabled?: boolean
  modules?: {
    key: string
    label: string
    permission: AuthType
  }[]
}
const menuList = ref<MenuAuthConfig[]>([])
function initMenuList() {
  menuList.value = PAGE_MENU_CONFIG.map(item => ({
    label: item.name,
    value: item.key,
    permission: 1,
    disabled: item.key === 'home',
    modules: item.module?.map(m => ({
      key: m.key,
      label: m.name,
      permission: 1,
    })),
  }))
}
initMenuList()

function updateMenuPermission(permissionValue: AuthType) {
  menuList.value.forEach((menu) => {
    if (!menu.disabled) {
      menu.permission = permissionValue
      // 同时更新所有模块的权限
      if (menu.modules && menu.modules.length > 0) {
        menu.modules.forEach((module) => {
          module.permission = permissionValue
        })
      }
    }
  })
}

// 生成权限配置内菜单配置的结构
const menuPermission = computed(() => {
  return menuList.value.reduce((acc, cur) => {
    // 创建新的菜单权限结构
    acc[cur.value] = {
      auth: cur.permission,
      module: (cur.modules && cur.modules.length > 0)
        ? cur.modules.map(m => ({ key: m.key, auth: m.permission }))
        : [],
    }
    return acc
  }, {} as MenuPermission)
})

// 奇异风口-风口概念-行业
const explodeCnDefault = ref('') // 中国
const explodeUsDefault = ref('') // 海外
const explodeCategoryLv1OptionsCN = ref<any[]>()
const explodeCategoryLv1OptionsUS = ref<any[]>()
// 市场探测
const msRealtimeCnt = ref(0)
const msWordExplorerCnt = ref(0)
const msEnabledCnt = ref(false)
const msFilterKeywords = ref('')
const msEntityType = ref('')
const msBrandIndustry = ref<string[]>([])
const brandIndustryOptions = ref<any[]>([])
const msRiskBrand = ref<number>()
const msRiskBrandOptions = ref<any[]>([])
const msEventIndustry = ref<string[]>([])
const msEventIndustryOptions = ref<any[]>([])

// 社媒智数
const smInfluenceIndustry = ref<string[]>([])
const smIndustryOptions = ref<any[]>([])
const smNeedsIndustry = ref<string[]>([])
const smAttentionIndustry = ref<string[]>([])
const smActivityIndustry = ref<string[]>([])
const smActivityIndustryOptions = ref<any[]>([])
const smBrand = ref<string>()
const smShareBrand = ref<number>()
const smSaEntity = ref<string>()
const smBrandOptions = ref<any[]>([])
const smSaEntityOptions = ref<any[]>([])

// 研究中心
const rfEnabledCnt = ref(false)
const rfWordExplorerCnt = ref(0)
const rfFilterKeywords = ref('')
const rfEntityType = ref('')
// AI 全息
const aiTimeRange = ref<string[]>([])
// APP 全息
const appTimeRange = ref<string[]>([])
// Rosetta X
const rsRealtimeCnt = ref(0)
const rsDatagptCnt = ref(0)
const rsAireportCnt = ref(0)
const rsEnabledCnt = ref(false)

const accountCurrentConfig = ref<AccountPermissionConfig>()

// 加载用户当前配置
async function loadUserCurrentAuthConfig(userCode?: string) {
  if (userCode) targetUserCode.value = userCode
  if (!targetUserCode.value) {
    resetConfig()
    return
  }
  const authConfigRes = await getUserAuthConfig(targetUserCode.value)
  if (authConfigRes.error_code) {
    ElMessage.error('获取用户权限配置失败')
    resetConfig()
  }
  else if (authConfigRes.data) {
    accountCurrentConfig.value = authConfigRes.data
    loadConfig(authConfigRes.data)
  }
  else {
    accountCurrentConfig.value = undefined
    resetConfig()
  }
}

// 回显用户当前配置
function loadConfig(config: AccountPermissionConfig) {
  const { product_permissions, data_permissions } = config
  resetConfig()
  // 菜单配置
  updateMenuPermission(3)

  const menuConfig = product_permissions.menu

  // 新版菜单配置 - { menu: { auth: number, module: Array<{key: string, auth: number}> } }
  Object.entries(menuConfig).forEach(([menuKey, menuData]) => {
    const menuItem = menuList.value.find(item => item.value === menuKey)
    if (menuItem) {
      // 设置主菜单权限
      menuItem.permission = menuData.auth ?? 0

      // 设置子模块权限
      if (!menuItem.modules) return
      if (menuData.module) {
        menuData.module.forEach((moduleData) => {
          const moduleItem = menuItem.modules?.find(m => m.key === moduleData.key)
          if (moduleItem) {
            moduleItem.permission = moduleData.auth
          }
        })
      }
      else {
        menuItem.modules.forEach((moduleItem) => {
          moduleItem.permission = 1
        })
      }
    }
  })

  // 奇异风口-风口概念-默认行业
  const tpExplodeCategoryConfig = getModuleFilter(accountCurrentConfig.value, 'tipping_point', 'exploding_word', 'keywords_category')
  if (tpExplodeCategoryConfig) {
    explodeCnDefault.value = tpExplodeCategoryConfig.china?.default?.[0] || ''
    explodeUsDefault.value = tpExplodeCategoryConfig.overseas?.default?.[0] || ''
  }
  // 市场探测-次数限制
  const msMaxLimit = data_permissions.market_sensor?.max_limit
  if (msMaxLimit && !isEmpty(msMaxLimit)) {
    msEnabledCnt.value = true
    msRealtimeCnt.value = msMaxLimit.live_detection || 0
    msWordExplorerCnt.value = msMaxLimit.word_explorer || 0
  }
  // 市场探测-探测对象
  const msAnalysisSubjectConfig = getModuleFilter(accountCurrentConfig.value, 'market_sensor', 'live_detection', 'analysis_subjects')
  if (msAnalysisSubjectConfig && msAnalysisSubjectConfig.default.length) {
    const subject = msAnalysisSubjectConfig.default[0]
    msFilterKeywords.value = subject.keywords
    msEntityType.value = subject.key
  }
  // 市场探测-市场情绪-品牌默认行业
  const msBrandIndustryConfig = getModuleFilter(accountCurrentConfig.value, 'market_sensor', 'market_psych', 'brands_industry')
  if (msBrandIndustryConfig && msBrandIndustryConfig.default) {
    msBrandIndustry.value = msBrandIndustryConfig.default
  }
  // 市场探测-市场风险-默认品牌
  const msRiskBrandConfig = getModuleFilter(accountCurrentConfig.value, 'market_sensor', 'market_risk', 'risk_brand')
  if (msRiskBrandConfig && msRiskBrandConfig.default) {
    msRiskBrand.value = msRiskBrandConfig.default
  }
  // 市场探测-大事件库-默认行业
  const msEventIndustryConfig = getModuleFilter(accountCurrentConfig.value, 'market_sensor', 'event_detection', 'event_industry')
  if (msEventIndustryConfig && msEventIndustryConfig.default) {
    msEventIndustry.value = msEventIndustryConfig.default
  }
  // 社媒智数-传播分析-品牌影响力默认行业
  const smInfluenceIndustryConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'influence', 'influence_industry')
  if (smInfluenceIndustryConfig && smInfluenceIndustryConfig.default) {
    smInfluenceIndustry.value = smInfluenceIndustryConfig.default
  }
  // 社媒智数-品牌心智-需求认知默认行业
  const smNeedsIndustryConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'brand_awareness', 'needs_industry')
  if (smNeedsIndustryConfig && smNeedsIndustryConfig.default) {
    smNeedsIndustry.value = smNeedsIndustryConfig.default
  }
  // 社媒智数-圈层透视-品类关注度默认行业
  const smAttentionIndustryConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'circle_perspective', 'attention_industry')
  if (smAttentionIndustryConfig && smAttentionIndustryConfig.default) {
    smAttentionIndustry.value = smAttentionIndustryConfig.default
  }
  // 社媒智数-引爆点-热门活动默认行业
  const smActivityIndustryConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'detonation_point', 'activity_industry')
  if (smActivityIndustryConfig && smActivityIndustryConfig.default) {
    smActivityIndustry.value = smActivityIndustryConfig.default
  }
  // 社媒智数-全局-默认品牌
  const smBrandConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'brand_awareness', 'brand')
  if (smBrandConfig && smBrandConfig.default) {
    smBrand.value = smBrandConfig.default
  }
  // 社媒智数-满意度分析-默认品牌
  const smSaEntityConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'satisfaction_analysis', 'entity')
  if (smSaEntityConfig && smSaEntityConfig.default) {
    smSaEntity.value = smSaEntityConfig.default
  }
  // 社媒智数-圈层透视-品牌占有率默认品牌
  const smShareBrandConfig = getModuleFilter(accountCurrentConfig.value, 'social_marketing', 'circle_perspective', 'share_brand')
  if (smShareBrandConfig && smShareBrandConfig.default) {
    smShareBrand.value = smShareBrandConfig.default
  }
  // 研究中心-次数限制
  const rfMaxLimit = data_permissions.research_findings?.max_limit
  if (rfMaxLimit && !isEmpty(rfMaxLimit)) {
    rfEnabledCnt.value = true
    rfWordExplorerCnt.value = rfMaxLimit.word_explorer || 0
  }
  // 研究中心-探测对象
  const rfAnalysisSubjectConfig = getModuleFilter(accountCurrentConfig.value, 'research_findings', 'word_explorer', 'analysis_subjects')
  if (rfAnalysisSubjectConfig && rfAnalysisSubjectConfig.default.length) {
    const subject = rfAnalysisSubjectConfig.default[0]
    rfFilterKeywords.value = subject.keywords
    rfEntityType.value = subject.key
  }
  // AI 全息-时间权限
  const aiRange = data_permissions.ai_product?.menu_time_range
  if (aiRange && aiRange.start && aiRange.end) {
    aiTimeRange.value = [aiRange.start, aiRange.end]
  }
  // APP 全息-时间权限
  const appRange = data_permissions.mobile_app?.menu_time_range
  if (appRange && appRange.start && appRange.end) {
    appTimeRange.value = [appRange.start, appRange.end]
  }
  // Rosetta X-次数限制
  const rsMAxLimit = data_permissions.rosetta_x?.max_limit
  if (rsMAxLimit && !isEmpty(rsMAxLimit)) {
    rsEnabledCnt.value = true
    rsRealtimeCnt.value = rsMAxLimit.realtime_detect || 0
    rsDatagptCnt.value = rsMAxLimit.datagpt || 0
    rsAireportCnt.value = rsMAxLimit.ai_report || 0
  }
}

function getModuleFilter(config: AccountPermissionConfig | undefined, menuKey: string, moduleKey: string, filterKey: string) {
  if (!config) return

  // @ts-expect-error ignore
  const menuConfig = config.data_permissions[menuKey]
  if (!menuConfig) return

  const moduleConfig = menuConfig.modules?.[moduleKey]

  const innerFilterResult = moduleConfig?.filters?.[filterKey]
  const outerFilterResult = menuConfig.filters?.[filterKey as keyof DataPermissionFilters]

  return innerFilterResult ?? outerFilterResult
}

function updateAccountConfig() {
  if (!accountCurrentConfig.value) return
  console.log('用户的原配置', accountCurrentConfig.value)
  const newConfig = cloneDeep(DEFAULT_AUTH_PERMISSIONS)
  merge(newConfig, accountCurrentConfig.value)
  newConfig.product_permissions.menu = menuPermission.value
  fillConfigDataPermission(newConfig)
  console.log('更新后的配置', newConfig)
  return newConfig
}

function generateNewConfig() {
  const newConfig = cloneDeep(DEFAULT_AUTH_PERMISSIONS)
  newConfig.product_permissions.menu = menuPermission.value
  fillConfigDataPermission(newConfig)
  console.log('生成的新配置', newConfig)
  return newConfig
}

function fillConfigDataPermission(config: AccountPermissionConfig) {
  // 奇异风口-风口概念-默认行业
  const tpExplodeCategoryConfig = getModuleFilter(config, 'tipping_point', 'exploding_word', 'keywords_category')
  if (tpExplodeCategoryConfig) {
    tpExplodeCategoryConfig.china.default = explodeCnDefault.value ? [explodeCnDefault.value] : ['1']
    tpExplodeCategoryConfig.overseas.default = explodeUsDefault.value ? [explodeUsDefault.value] : ['14']
  }
  // 市场探测-次数限制
  const msMaxLimit = msEnabledCnt.value ? {
    live_detection: Number(msRealtimeCnt.value),
    word_explorer: Number(msWordExplorerCnt.value),
  } : {}
  config.data_permissions.market_sensor.max_limit = msMaxLimit
  // 市场探测-探测对象
  const msAnalysisSubjectConfig = getModuleFilter(config, 'market_sensor', 'live_detection', 'analysis_subjects')
  if (msAnalysisSubjectConfig) {
    const entityType = msEntityType.value || 'supermarket'
    const keywords = msFilterKeywords.value || '沃尔玛'
    msAnalysisSubjectConfig.default = [{
      filtered_words: '',
      key: entityType,
      keywords,
      type: getEntityTypeLabel(entityType),
    }]
    config.data_permissions.market_sensor.filters.analysis_subjects.default = [{
      filtered_words: '',
      key: entityType,
      keywords,
      type: getEntityTypeLabel(entityType) || '',
    }]
  }
  // 市场探测-市场情绪-品牌默认行业
  const msBrandIndustryConfig = getModuleFilter(config, 'market_sensor', 'market_psych', 'brands_industry')
  if (msBrandIndustryConfig) {
    msBrandIndustryConfig.default = msBrandIndustry.value
  }
  // 市场探测-市场风险-默认品牌
  const msRiskBrandConfig = getModuleFilter(config, 'market_sensor', 'market_risk', 'risk_brand')
  if (msRiskBrandConfig) {
    msRiskBrandConfig.default = msRiskBrand.value
  }
  // 市场探测-大事件库-默认行业
  const msEventIndustryConfig = getModuleFilter(config, 'market_sensor', 'event_detection', 'event_industry')
  if (msEventIndustryConfig) {
    msEventIndustryConfig.default = msEventIndustry.value
  }
  // 社媒智数-传播分析-品牌影响力默认行业
  const smInfluenceIndustryConfig = getModuleFilter(config, 'social_marketing', 'influence', 'influence_industry')
  if (smInfluenceIndustryConfig) {
    smInfluenceIndustryConfig.default = smInfluenceIndustry.value
  }
  // 社媒智数-品牌心智-需求认知默认行业
  const smNeedsIndustryConfig = getModuleFilter(config, 'social_marketing', 'brand_awareness', 'needs_industry')
  if (smNeedsIndustryConfig) {
    smNeedsIndustryConfig.default = smNeedsIndustry.value
  }
  // 社媒智数-圈层透视-品类关注度默认行业
  const smAttentionIndustryConfig = getModuleFilter(config, 'social_marketing', 'circle_perspective', 'attention_industry')
  if (smAttentionIndustryConfig) {
    smAttentionIndustryConfig.default = smAttentionIndustry.value
  }
  // 社媒智数-引爆点-热门活动默认行业
  const smActivityIndustryConfig = getModuleFilter(config, 'social_marketing', 'detonation_point', 'activity_industry')
  if (smActivityIndustryConfig) {
    smActivityIndustryConfig.default = smActivityIndustry.value
  }
  // 社媒智数-全局-默认品牌
  const smBrandConfig = getModuleFilter(config, 'social_marketing', 'brand_awareness', 'brand')
  if (smBrandConfig) {
    smBrandConfig.default = smBrand.value
  }
  // 社媒智数-满意度分析-默认品牌
  const smSaEntityConfig = getModuleFilter(config, 'social_marketing', 'satisfaction_analysis', 'entity')
  if (smSaEntityConfig) {
    smSaEntityConfig.default = smSaEntity.value
  }
  // 社媒智数-圈层透视-品牌占有率默认品牌
  const smShareBrandConfig = getModuleFilter(config, 'social_marketing', 'circle_perspective', 'share_brand')
  if (smShareBrandConfig) {
    smShareBrandConfig.default = smShareBrand.value
  }
  // 研究中心-次数限制
  const rfMaxLimit = rfEnabledCnt.value ? {
    word_explorer: Number(rfWordExplorerCnt.value),
  } : {}
  config.data_permissions.research_findings.max_limit = rfMaxLimit
  // 研究中心-探测对象
  const rfAnalysisSubjectConfig = getModuleFilter(config, 'research_findings', 'word_explorer', 'analysis_subjects')
  if (rfAnalysisSubjectConfig) {
    const entityType = rfEntityType.value || 'supermarket'
    const keywords = rfFilterKeywords.value || '沃尔玛'
    rfAnalysisSubjectConfig.default = [{
      filtered_words: '',
      key: entityType,
      keywords,
      type: getEntityTypeLabel(entityType),
    }]
    config.data_permissions.research_findings.filters.analysis_subjects.default = [{
      filtered_words: '',
      key: entityType,
      keywords,
      type: getEntityTypeLabel(entityType) || '',
    }]
  }
  // AI 全息-时间权限
  const aiRange = aiTimeRange.value?.length ? {
    start: getMonthStart(aiTimeRange.value[0]),
    end: getMonthEnd(aiTimeRange.value[1]),
  } : {}
  config.data_permissions.ai_product.menu_time_range = aiRange
  // APP 全息-时间权限
  const appRange = appTimeRange.value?.length ? {
    start: getMonthStart(appTimeRange.value[0]),
    end: getMonthEnd(appTimeRange.value[1]),
  } : {}
  config.data_permissions.mobile_app.menu_time_range = appRange
  // Rosetta X-次数限制
  const rsMaxLimit = rsEnabledCnt.value ? {
    realtime_detect: Number(rsRealtimeCnt.value),
    datagpt: Number(rsDatagptCnt.value),
    aireport: Number(rsAireportCnt.value),
  } : {}
  config.data_permissions.rosetta_x.max_limit = rsMaxLimit
}

// 生成配置
async function generateConfig() {
  const config = accountCurrentConfig.value ? updateAccountConfig() : generateNewConfig()
  if (!config) {
    ElMessage.error('生成配置异常, 请联系管理员排查')
    return
  }
  if (!targetUserCode.value) {
    statusMsg.value = '用户编码不能为空'
    return
  }
  statusMsg.value = ''
  const res = await updateUserAuthConfig(targetUserCode.value, config)
  if (res.error_code) {
    statusMsg.value = res.error_msg
    return
  }
  statusMsg.value = ''
  ElMessage.success('更新成功')
  emits('success')
}

// 初始化配置变量值
function resetConfig() {
  updateMenuPermission(1)
  explodeCnDefault.value = ''
  explodeUsDefault.value = ''
  msRealtimeCnt.value = 0
  msWordExplorerCnt.value = 0
  msEnabledCnt.value = false
  msFilterKeywords.value = ''
  msEntityType.value = ''
  aiTimeRange.value = []
  appTimeRange.value = []
  rsRealtimeCnt.value = 0
  rsDatagptCnt.value = 0
  rsAireportCnt.value = 0
  rsEnabledCnt.value = false
  msRiskBrand.value = undefined
  msBrandIndustry.value = []
  msEventIndustry.value = []
  smInfluenceIndustry.value = []
  smNeedsIndustry.value = []
  smAttentionIndustry.value = []
  smActivityIndustry.value = []
  smBrand.value = undefined
  smSaEntity.value = undefined
  smShareBrand.value = undefined
}

// 格式化日期为月份的第一天
function getMonthStart(date: string) {
  return dayjs(date).startOf('month').format('YYYY-MM-DD')
}
// 格式化日期为月份的最后一天
function getMonthEnd(date: string) {
  return dayjs(date).endOf('month').format('YYYY-MM-DD')
}
// 获取实体类型的中文名称
function getEntityTypeLabel(value: string) {
  return ENTITY_TYPE_OPTIONS.find(item => item.value === value)?.label
}

defineExpose({
  loadUserCurrentAuthConfig,
})

onMounted(() => {
  getBrandsIndustry().then((res) => {
    brandIndustryOptions.value = res
    msEventIndustryOptions.value = res.map((item) => {
      return {
        ...item,
        children: [],
      }
    })
  })
  getSMIndustry().then((res) => {
    smIndustryOptions.value = res
    smActivityIndustryOptions.value = res.map((item) => {
      return {
        ...item,
        children: [],
      }
    })
  })
  getRiskBrand().then((res) => {
    msRiskBrandOptions.value = res
  })
  getSMBrand().then((res) => {
    smBrandOptions.value = res
  })
  getSMSaEntity().then((res) => {
    smSaEntityOptions.value = res
  })
  getExplodingTopicsCategoryLv1().then((res) => {
    explodeCategoryLv1OptionsUS.value = res.filter(item => item.region === 'overseas')
    explodeCategoryLv1OptionsCN.value = res.filter(item => item.region === 'china')
  })
})
</script>

<style lang="scss" scoped>
.auth-setting {
  @apply h-90vh flex flex-col;
  .title-1 {
    font-weight: bold;
    font-size: 18px;
    line-height: 30px;
  }
  .title-2 {
    font-weight: bold;
    font-size: 16px;
    line-height: 28px;
  }
}

.auth-setting-card {
  width: 300px;
}
:deep(.el-card .el-card__header) {
  padding: 10px;
  font-size: 14px;
}
:deep(.el-card .el-card__body) {
  padding: 16px;
}

:deep(.el-collapse .el-collapse-item__header) {
  font-weight: bold;
  font-size: 14px;
}
</style>
