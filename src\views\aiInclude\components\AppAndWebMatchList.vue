<template>
  <div class="flex flex-col gap-16 h-full">
    <Handsontable
      ref="hotTableRef"
      :columns="columns"
      :data="tableData"
      :post-transform="postTransform"
    />
    <el-button class="self-end" type="primary" @click="onConfirm">
      保存
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { TaskStepMatchData } from '../hooks/type'
import { useData } from '../hooks/useData'
import { useFilter } from '../hooks/useFilter'

const emit = defineEmits<{
  (e: 'saveSuccess'): void
}>()

const {
  getAiWebTaskStepMatchData,
  updateAiWebTaskStepMatchData,
} = useData()
const {
  taskId,
  allAiList,
} = useFilter()

const hotTableRef = shallowRef()

const columns = computed(() => {
  return [
    {
      name: 'web_id',
      label: 'WEB ID',
      editor: false,
    },
    {
      name: 'web_name',
      label: 'WEB名称',
      editor: false,
    },
    {
      name: 'app_name',
      label: 'APP名称',
      type: 'autocomplete',
      source: allAiList.value.map(_ => _.name),
    },
  ]
})

function postTransform(list: TaskStepMatchData[]) {
  return list.map((data) => {
    const res: any = {
      ...data,
    }
    res.app_id = allAiList.value.find(_ => _.name === data.app_name)?.app_id
    return res
  })
}

const tableData = ref<TaskStepMatchData[]>([])

async function loadData() {
  const { data } = await getAiWebTaskStepMatchData(taskId.value)
  tableData.value = data
}

loadData()

async function onConfirm() {
  const changedData = hotTableRef.value?.getData()
  const { error_code, error_msg } = await updateAiWebTaskStepMatchData(taskId.value, changedData)
  if (error_code === 0) {
    ElMessage.success('保存成功')
    emit('saveSuccess')
  }
  else {
    ElMessage.error(error_msg)
  }
}
</script>

<style scoped lang="scss">

</style>
