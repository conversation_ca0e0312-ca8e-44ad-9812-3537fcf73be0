<template>
  <div class="flex-center flex-col gap-16">
    <Bicycle class="h-280 w-280" />
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>

<script setup lang="ts">
import { Bicycle } from '@element-plus/icons-vue'
import { useFilter } from '../hooks/useFilter'

const emit = defineEmits<{
  (e: 'load'): void
}>()

const { currentStep } = useFilter()

// 状态相关
const statusText = ref('自动化处理中，请稍候...')
const POLL_INTERVAL = 3000
// 轮询定时器
let pollTimer: NodeJS.Timer | null = null

// TODO 自动轮询接口，获取任务最新状态
async function getStepStatus() {
  try {
    emit('load')
  }
  catch (err) {
    clearPollTimer()
    statusText.value = '获取状态失败，请刷新页面重试'
  }
}

// 创建轮询定时器
function createPollTimer() {
  clearPollTimer()
  // 重置状态
  statusText.value = '自动化处理中，请稍候...'

  // 立即执行一次
  getStepStatus()
  // 创建新的定时器
  pollTimer = setInterval(getStepStatus, POLL_INTERVAL)
}

// 清除定时器
function clearPollTimer() {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 监听 step 变化，重新创建定时器
// 只有当 step 变化且组件仍然挂载时才重新创建
watch(() => currentStep.value, (newStep, oldStep) => {
  if (newStep !== oldStep) {
    createPollTimer()
  }
})

// 组件挂载时创建定时器
onMounted(() => {
  createPollTimer()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  clearPollTimer()
})
</script>

<style lang="scss" scoped>
:deep(.el-empty__description p) {
  font-size: 16px;
}
</style>
