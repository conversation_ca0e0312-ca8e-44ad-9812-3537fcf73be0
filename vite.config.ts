import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import svgLoader from 'vite-svg-loader'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { URL, fileURLToPath } from 'node:url'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'

const proxyTarget = 'dev'
const proxyTargetMap = {
  dev: 'http://stage.xsignal-ai.com/',
  prd: 'https://xsignal-ai.com',
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const IS_PROD = mode === 'production'
  const publicPath = '/config/'
  return {
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
      extensions: ['.js', '.ts', '.vue', '.json'],
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/styles/theme/light/theme.scss" as *;',
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(),
      svgLoader(),
      UnoCSS(),

      // Auto import APIs on-demand (https://github.com/antfu/unplugin-auto-import)
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          {
            'vue-router': [
              'createRouter',
              'createWebHashHistory',
            ],
          },
          'pinia',
          '@vueuse/core',
          {
            'vue-i18n': ['useI18n'],
          },
          {
            from: 'vue-router',
            imports: [
              'RouteLocationRaw',
              'RouteRecordRaw',
            ],
            type: true,
          },
        ],
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass',
          }),
          IconsResolver({ prefix: 'Icon' }),
        ],
        dirs: [
          'src/apis/**',
          'src/stores/**',
        ],
        dts: 'src/auto-imports.d.ts',
        eslintrc: {
          enabled: true,
        },
      }),

      // Auto import components on-demand (https://github.com/antfu/unplugin-vue-components)
      Components({
        types: [{
          from: 'vue-router',
          names: ['RouterLink', 'RouterView'],
        }],
        resolvers: [
          IconsResolver({
            enabledCollections: ['ep'],
          }),
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
        dirs: [
          'src/components/**',
        ],
        dts: 'src/components.d.ts',
      }),

      // https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n
      VueI18nPlugin({
        runtimeOnly: true,
      }),
      Icons({
        autoInstall: true,
      }),
    ],

    server: {
      host: true,
      port: 3334,
      proxy: {
        '/eagle/v1': {
          target: proxyTargetMap[proxyTarget],
          changeOrigin: true,
        },
        '/ops/v2': {
          target: proxyTargetMap[proxyTarget],
          changeOrigin: true,
        },
        '/data': {
          target: proxyTargetMap[proxyTarget],
          changeOrigin: true,
        },
      },
    },
    base: publicPath,
    build: {
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'static/js/[name]-[hash].js', // 包的入口文件名称
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片
        },
      },
    },
  }
})
