import type { RouteLocationNormalized } from 'vue-router'
import { useAccountStore } from '@/stores/account'
import NProgress from '@/config/nprogress'
import { getToken } from '@/utils/cookies'
import { isEmpty } from 'lodash-es'
import { commonRoutes } from './index'
import { useAccountData } from '@/views/account/hooks/useAccountData'

NProgress.configure({ showSpinner: false })

async function initNecessaryData() {
  const { getBrandsIndustry } = useAccountData()
  const { getSmIndustry, getSmEntityIndustry } = useAccountStore()
  const { industryOptions, smIndustryOptions, smEntityIndustryOptions } = storeToRefs(useAccountStore())

  if (isEmpty(industryOptions.value)) {
    industryOptions.value = await getBrandsIndustry()
    industryOptions.value.unshift({
      mp_industry_code: '0',
      industry_name: '通用',
      industry_en: 'Common',
      industry_level: 1,
      industry_parent: '0',
      enabled: 1,
      id: 1,
    })
  }

  if (isEmpty(smIndustryOptions.value)) {
    smIndustryOptions.value = await getSmIndustry()
  }

  if (isEmpty(smEntityIndustryOptions.value)) {
    smEntityIndustryOptions.value = await getSmEntityIndustry()
  }
}

async function beforeEachGuard(to: RouteLocationNormalized) {
  NProgress.start()

  const accountStore = useAccountStore()

  // 1. 根据有没有token判断是否已登录
  if (accountStore.token && getToken()) {
    accountStore.getUserInfo()
    if (to.path === '/login') {
      // 如果已经登录，并准备进入 Login 页面，则重定向到首页
      NProgress.done()
      return { path: '/' }
    }

    if (isEmpty(accountStore.userPermission)) {
      // 2. 如果没有权限，则获取用户信息
      await accountStore.getUserInfo()
    }

    await initNecessaryData()

    // 3. 路由鉴权
    const authRoutes = commonRoutes.filter((route) => {
      const { hasMenuPermission } = useAccountStore()
      return hasMenuPermission(route.name!.toString().split('@')[0])
    })
    if (to.name) {
      const route = authRoutes.find(route => route.name === to.name!.toString().split('@')[0])
      if (!route) {
        return { name: authRoutes[0].name }
      }
    }

    return true
  }
  else {
    // 未登录，跳转登录页
    NProgress.done()
    if (to.path === '/login') {
      return true
    }
    return { path: '/login' }
  }
}
function afterEachGuard(to: RouteLocationNormalized) {
  NProgress.done()
}

export function usePermission() {
  return {
    beforeEachGuard,
    afterEachGuard,
  }
}
