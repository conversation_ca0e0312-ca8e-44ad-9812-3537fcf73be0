# xsignal-frontend-config

## Quick Start

```bash
# 安装依赖
pnpm install
# 运行
pnpm dev
# build
pnpm build
```


## 配置 nginx

front1 机器

htpasswd -c /root/website/conf.d/config.htpasswd xsadmin 

location /config/ {
    auth_basic "Restricted Access";
    auth_basic_user_file /home/<USER>/conf.d/config.htpasswd;
}

stage1 机器 

htpasswd -c /root/website/conf.d/config.htpasswd xsadmin 

location /config/ {
    auth_basic "Restricted Access";
    auth_basic_user_file /home/<USER>/conf.d/config.htpasswd;
}