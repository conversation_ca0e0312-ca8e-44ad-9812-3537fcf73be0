.dark-block {
  /*dark*/
  color-scheme: dark;
  --color-prettylights-syntax-comment: #8b949e;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-variable: #ffa657;
  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
  --color-prettylights-syntax-carriage-return-text: #f0f6fc;
  --color-prettylights-syntax-carriage-return-bg: #b62324;
  --color-prettylights-syntax-string-regexp: #7ee787;
  --color-prettylights-syntax-markup-list: #f2cc60;
  --color-prettylights-syntax-markup-heading: #1f6feb;
  --color-prettylights-syntax-markup-italic: #c9d1d9;
  --color-prettylights-syntax-markup-bold: #c9d1d9;
  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
  --color-prettylights-syntax-markup-deleted-bg: #67060c;
  --color-prettylights-syntax-markup-inserted-text: #aff5b4;
  --color-prettylights-syntax-markup-inserted-bg: #033a16;
  --color-prettylights-syntax-markup-changed-text: #ffdfb6;
  --color-prettylights-syntax-markup-changed-bg: #5a1e02;
  --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
  --color-prettylights-syntax-markup-ignored-bg: #1158c7;
  --color-prettylights-syntax-meta-diff-range: #d2a8ff;
  --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
  --color-fg-default: #e6edf3;
  --color-fg-muted: #7d8590;
  --color-fg-subtle: #6e7681;
  --color-canvas-default: #0d1117;
  --color-canvas-subtle: #161b22;
  --color-border-default: #30363d;
  --color-border-muted: #21262d;
  --color-neutral-muted: rgba(110, 118, 129, 0.4);
  --color-accent-fg: #2f81f7;
  --color-accent-emphasis: #1f6feb;
  --color-attention-subtle: rgba(187, 128, 9, 0.15);
  --color-danger-fg: #f85149;
}

.light-block {
  /*light*/
  color-scheme: light;
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-fg-default: #1F2328;
  --color-fg-muted: #656d76;
  --color-fg-subtle: #6e7781;
  --color-canvas-default: #ffffff;
  --color-canvas-subtle: #f6f8fa;
  --color-border-default: #d0d7de;
  --color-border-muted: hsla(210, 18%, 87%, 1);
  --color-neutral-muted: rgba(175, 184, 193, 0.2);
  --color-accent-fg: #0969da;
  --color-accent-emphasis: #0969da;
  --color-attention-subtle: #fff8c5;
  --color-danger-fg: #d1242f;
}


.github-markdown-body {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  color: var(--color-fg-default);
  /*background-color: var(--color-canvas-default);*/
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.github-markdown-body .octicon {
  display: inline-block;
  fill: currentColor;
  vertical-align: text-bottom;
}

.github-markdown-body h1:hover .anchor .octicon-link:before,
.github-markdown-body h2:hover .anchor .octicon-link:before,
.github-markdown-body h3:hover .anchor .octicon-link:before,
.github-markdown-body h4:hover .anchor .octicon-link:before,
.github-markdown-body h5:hover .anchor .octicon-link:before,
.github-markdown-body h6:hover .anchor .octicon-link:before {
  width: 16px;
  height: 16px;
  content: ' ';
  display: inline-block;
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
  mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
}

.github-markdown-body details,
.github-markdown-body figcaption,
.github-markdown-body figure {
  display: block;
}

.github-markdown-body summary {
  display: list-item;
}

.github-markdown-body [hidden] {
  display: none !important;
}

.github-markdown-body a {
  background-color: transparent;
  color: var(--color-accent-fg);
  text-decoration: none;
}

.github-markdown-body abbr[title] {
  border-bottom: none;
  text-decoration: underline dotted;
}

.github-markdown-body b,
.github-markdown-body strong {
  font-weight: var(--base-text-weight-semibold, 600);
}

.github-markdown-body dfn {
  font-style: italic;
}

.github-markdown-body h1 {
  margin: .67em 0;
  font-weight: var(--base-text-weight-semibold, 600);
  padding-bottom: .3em;
  font-size: 2em;
  border-bottom: 1px solid var(--color-border-muted);
}

.github-markdown-body mark {
  background-color: var(--color-attention-subtle);
  color: var(--color-fg-default);
}

.github-markdown-body small {
  font-size: 90%;
}

.github-markdown-body sub,
.github-markdown-body sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.github-markdown-body sub {
  bottom: -0.25em;
}

.github-markdown-body sup {
  top: -0.5em;
}

.github-markdown-body img {
  border-style: none;
  max-width: 100%;
  box-sizing: content-box;
  background-color: var(--color-canvas-default);
}

.github-markdown-body code,
.github-markdown-body kbd,
.github-markdown-body pre,
.github-markdown-body samp {
  font-family: monospace;
  font-size: 1em;
}

.github-markdown-body figure {
  margin: 1em 40px;
}

.github-markdown-body hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  border-bottom: 1px solid var(--color-border-muted);
  height: .25em;
  padding: 0;
  margin: 24px 0;
  background-color: var(--color-border-default);
  border: 0;
}

.github-markdown-body input {
  font: inherit;
  margin: 0;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.github-markdown-body [type=button],
.github-markdown-body [type=reset],
.github-markdown-body [type=submit] {
  -webkit-appearance: button;
}

.github-markdown-body [type=checkbox],
.github-markdown-body [type=radio] {
  box-sizing: border-box;
  padding: 0;
}

.github-markdown-body [type=number]::-webkit-inner-spin-button,
.github-markdown-body [type=number]::-webkit-outer-spin-button {
  height: auto;
}

.github-markdown-body [type=search]::-webkit-search-cancel-button,
.github-markdown-body [type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

.github-markdown-body ::-webkit-input-placeholder {
  color: inherit;
  opacity: .54;
}

.github-markdown-body ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

.github-markdown-body a:hover {
  text-decoration: underline;
}

.github-markdown-body ::placeholder {
  color: var(--color-fg-subtle);
  opacity: 1;
}

.github-markdown-body hr::before {
  display: table;
  content: "";
}

.github-markdown-body hr::after {
  display: table;
  clear: both;
  content: "";
}

.github-markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
}

.github-markdown-body td,
.github-markdown-body th {
  padding: 0;
}

.github-markdown-body details summary {
  cursor: pointer;
}

.github-markdown-body details:not([open]) > *:not(summary) {
  display: none !important;
}

.github-markdown-body a:focus,
.github-markdown-body [role=button]:focus,
.github-markdown-body input[type=radio]:focus,
.github-markdown-body input[type=checkbox]:focus {
  outline: 2px solid var(--color-accent-fg);
  outline-offset: -2px;
  box-shadow: none;
}

.github-markdown-body a:focus:not(:focus-visible),
.github-markdown-body [role=button]:focus:not(:focus-visible),
.github-markdown-body input[type=radio]:focus:not(:focus-visible),
.github-markdown-body input[type=checkbox]:focus:not(:focus-visible) {
  outline: solid 1px transparent;
}

.github-markdown-body a:focus-visible,
.github-markdown-body [role=button]:focus-visible,
.github-markdown-body input[type=radio]:focus-visible,
.github-markdown-body input[type=checkbox]:focus-visible {
  outline: 2px solid var(--color-accent-fg);
  outline-offset: -2px;
  box-shadow: none;
}

.github-markdown-body a:not([class]):focus,
.github-markdown-body a:not([class]):focus-visible,
.github-markdown-body input[type=radio]:focus,
.github-markdown-body input[type=radio]:focus-visible,
.github-markdown-body input[type=checkbox]:focus,
.github-markdown-body input[type=checkbox]:focus-visible {
  outline-offset: 0;
}

.github-markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  line-height: 10px;
  color: var(--color-fg-default);
  vertical-align: middle;
  background-color: var(--color-canvas-subtle);
  border: solid 1px var(--color-neutral-muted);
  border-bottom-color: var(--color-neutral-muted);
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 var(--color-neutral-muted);
}

.github-markdown-body h1,
.github-markdown-body h2,
.github-markdown-body h3,
.github-markdown-body h4,
.github-markdown-body h5,
.github-markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: var(--base-text-weight-semibold, 600);
  line-height: 1.25;
}

.github-markdown-body h2 {
  font-weight: var(--base-text-weight-semibold, 600);
  padding-bottom: .3em;
  font-size: 1.5em;
  border-bottom: 1px solid var(--color-border-muted);
}

.github-markdown-body h3 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: 1.25em;
}

.github-markdown-body h4 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: 1em;
}

.github-markdown-body h5 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: .875em;
}

.github-markdown-body h6 {
  font-weight: var(--base-text-weight-semibold, 600);
  font-size: .85em;
  color: var(--color-fg-muted);
}

.github-markdown-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.github-markdown-body blockquote {
  margin: 0;
  padding: 0 1em;
  color: var(--color-fg-muted);
  border-left: .25em solid var(--color-border-default);
}

.github-markdown-body ul,
.github-markdown-body ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 2em;
}

.github-markdown-body ol ol,
.github-markdown-body ul ol {
  list-style-type: lower-roman;
}

.github-markdown-body ul ul ol,
.github-markdown-body ul ol ol,
.github-markdown-body ol ul ol,
.github-markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.github-markdown-body dd {
  margin-left: 0;
}

.github-markdown-body tt,
.github-markdown-body code,
.github-markdown-body samp {
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 12px;
}

.github-markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 12px;
  word-wrap: normal;
}

.github-markdown-body .octicon {
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.github-markdown-body input::-webkit-outer-spin-button,
.github-markdown-body input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
  appearance: none;
}

.github-markdown-body::before {
  display: table;
  content: "";
}

.github-markdown-body::after {
  display: table;
  clear: both;
  content: "";
}

.github-markdown-body > *:first-child {
  margin-top: 0 !important;
}

.github-markdown-body > *:last-child {
  margin-bottom: 0 !important;
}

.github-markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.github-markdown-body .absent {
  color: var(--color-danger-fg);
}

.github-markdown-body .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.github-markdown-body .anchor:focus {
  outline: none;
}

.github-markdown-body p,
.github-markdown-body blockquote,
.github-markdown-body ul,
.github-markdown-body ol,
.github-markdown-body dl,
.github-markdown-body table,
.github-markdown-body pre,
.github-markdown-body details {
  margin-top: 0;
  margin-bottom: 16px;
}

.github-markdown-body blockquote > :first-child {
  margin-top: 0;
}

.github-markdown-body blockquote > :last-child {
  margin-bottom: 0;
}

.github-markdown-body h1 .octicon-link,
.github-markdown-body h2 .octicon-link,
.github-markdown-body h3 .octicon-link,
.github-markdown-body h4 .octicon-link,
.github-markdown-body h5 .octicon-link,
.github-markdown-body h6 .octicon-link {
  color: var(--color-fg-default);
  vertical-align: middle;
  visibility: hidden;
}

.github-markdown-body h1:hover .anchor,
.github-markdown-body h2:hover .anchor,
.github-markdown-body h3:hover .anchor,
.github-markdown-body h4:hover .anchor,
.github-markdown-body h5:hover .anchor,
.github-markdown-body h6:hover .anchor {
  text-decoration: none;
}

.github-markdown-body h1:hover .anchor .octicon-link,
.github-markdown-body h2:hover .anchor .octicon-link,
.github-markdown-body h3:hover .anchor .octicon-link,
.github-markdown-body h4:hover .anchor .octicon-link,
.github-markdown-body h5:hover .anchor .octicon-link,
.github-markdown-body h6:hover .anchor .octicon-link {
  visibility: visible;
}

.github-markdown-body h1 tt,
.github-markdown-body h1 code,
.github-markdown-body h2 tt,
.github-markdown-body h2 code,
.github-markdown-body h3 tt,
.github-markdown-body h3 code,
.github-markdown-body h4 tt,
.github-markdown-body h4 code,
.github-markdown-body h5 tt,
.github-markdown-body h5 code,
.github-markdown-body h6 tt,
.github-markdown-body h6 code {
  padding: 0 .2em;
  font-size: inherit;
}

.github-markdown-body summary h1,
.github-markdown-body summary h2,
.github-markdown-body summary h3,
.github-markdown-body summary h4,
.github-markdown-body summary h5,
.github-markdown-body summary h6 {
  display: inline-block;
}

.github-markdown-body summary h1 .anchor,
.github-markdown-body summary h2 .anchor,
.github-markdown-body summary h3 .anchor,
.github-markdown-body summary h4 .anchor,
.github-markdown-body summary h5 .anchor,
.github-markdown-body summary h6 .anchor {
  margin-left: -40px;
}

.github-markdown-body summary h1,
.github-markdown-body summary h2 {
  padding-bottom: 0;
  border-bottom: 0;
}

.github-markdown-body ul.no-list,
.github-markdown-body ol.no-list {
  padding: 0;
  list-style-type: none;
}

.github-markdown-body ol[type=a] {
  list-style-type: lower-alpha;
}

.github-markdown-body ol[type=A] {
  list-style-type: upper-alpha;
}

.github-markdown-body ol[type=i] {
  list-style-type: lower-roman;
}

.github-markdown-body ol[type=I] {
  list-style-type: upper-roman;
}

.github-markdown-body ol[type="1"] {
  list-style-type: decimal;
}

.github-markdown-body div > ol:not([type]) {
  list-style-type: decimal;
}

.github-markdown-body ul ul,
.github-markdown-body ul ol,
.github-markdown-body ol ol,
.github-markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.github-markdown-body li > p {
  margin-top: 16px;
}

.github-markdown-body li + li {
  margin-top: .25em;
}

.github-markdown-body dl {
  padding: 0;
}

.github-markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: var(--base-text-weight-semibold, 600);
}

.github-markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.github-markdown-body table th {
  font-weight: var(--base-text-weight-semibold, 600);
}

.github-markdown-body table th,
.github-markdown-body table td {
  padding: 6px 13px;
  border: 1px solid var(--color-border-default);
}

.github-markdown-body table td > :last-child {
  margin-bottom: 0;
}

.github-markdown-body table tr {
  background-color: var(--color-canvas-default);
  border-top: 1px solid var(--color-border-muted);
}

.github-markdown-body table tr:nth-child(2n) {
  background-color: var(--color-canvas-subtle);
}

.github-markdown-body table img {
  background-color: transparent;
}

.github-markdown-body img[align=right] {
  padding-left: 20px;
}

.github-markdown-body img[align=left] {
  padding-right: 20px;
}

.github-markdown-body .emoji {
  max-width: none;
  vertical-align: text-top;
  background-color: transparent;
}

.github-markdown-body span.frame {
  display: block;
  overflow: hidden;
}

.github-markdown-body span.frame > span {
  display: block;
  float: left;
  width: auto;
  padding: 7px;
  margin: 13px 0 0;
  overflow: hidden;
  border: 1px solid var(--color-border-default);
}

.github-markdown-body span.frame span img {
  display: block;
  float: left;
}

.github-markdown-body span.frame span span {
  display: block;
  padding: 5px 0 0;
  clear: both;
  color: var(--color-fg-default);
}

.github-markdown-body span.align-center {
  display: block;
  overflow: hidden;
  clear: both;
}

.github-markdown-body span.align-center > span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: center;
}

.github-markdown-body span.align-center span img {
  margin: 0 auto;
  text-align: center;
}

.github-markdown-body span.align-right {
  display: block;
  overflow: hidden;
  clear: both;
}

.github-markdown-body span.align-right > span {
  display: block;
  margin: 13px 0 0;
  overflow: hidden;
  text-align: right;
}

.github-markdown-body span.align-right span img {
  margin: 0;
  text-align: right;
}

.github-markdown-body span.float-left {
  display: block;
  float: left;
  margin-right: 13px;
  overflow: hidden;
}

.github-markdown-body span.float-left span {
  margin: 13px 0 0;
}

.github-markdown-body span.float-right {
  display: block;
  float: right;
  margin-left: 13px;
  overflow: hidden;
}

.github-markdown-body span.float-right > span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: right;
}

.github-markdown-body code,
.github-markdown-body tt {
  padding: .2em .4em;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  background-color: var(--color-neutral-muted);
  border-radius: 6px;
}

.github-markdown-body code br,
.github-markdown-body tt br {
  display: none;
}

.github-markdown-body del code {
  text-decoration: inherit;
}

.github-markdown-body samp {
  font-size: 85%;
}

.github-markdown-body pre code {
  font-size: 100%;
}

.github-markdown-body pre > code {
  padding: 0;
  margin: 0;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.github-markdown-body .highlight {
  margin-bottom: 16px;
}

.github-markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.github-markdown-body .highlight pre,
.github-markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  color: var(--color-fg-default);
  background-color: var(--color-canvas-subtle);
  border-radius: 6px;
}

.github-markdown-body pre code,
.github-markdown-body pre tt {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.github-markdown-body .csv-data td,
.github-markdown-body .csv-data th {
  padding: 5px;
  overflow: hidden;
  font-size: 12px;
  line-height: 1;
  text-align: left;
  white-space: nowrap;
}

.github-markdown-body .csv-data .blob-num {
  padding: 10px 8px 9px;
  text-align: right;
  background: var(--color-canvas-default);
  border: 0;
}

.github-markdown-body .csv-data tr {
  border-top: 0;
}

.github-markdown-body .csv-data th {
  font-weight: var(--base-text-weight-semibold, 600);
  background: var(--color-canvas-subtle);
  border-top: 0;
}

.github-markdown-body [data-footnote-ref]::before {
  content: "[";
}

.github-markdown-body [data-footnote-ref]::after {
  content: "]";
}

.github-markdown-body .footnotes {
  font-size: 12px;
  color: var(--color-fg-muted);
  border-top: 1px solid var(--color-border-default);
}

.github-markdown-body .footnotes ol {
  padding-left: 16px;
}

.github-markdown-body .footnotes ol ul {
  display: inline-block;
  padding-left: 16px;
  margin-top: 16px;
}

.github-markdown-body .footnotes li {
  position: relative;
}

.github-markdown-body .footnotes li:target::before {
  position: absolute;
  top: -8px;
  right: -8px;
  bottom: -8px;
  left: -24px;
  pointer-events: none;
  content: "";
  border: 2px solid var(--color-accent-emphasis);
  border-radius: 6px;
}

.github-markdown-body .footnotes li:target {
  color: var(--color-fg-default);
}

.github-markdown-body .footnotes .data-footnote-backref g-emoji {
  font-family: monospace;
}

.github-markdown-body [popover] {
  background: canvas;
  border: solid;
  color: canvastext;
  height: fit-content;
  inset: 0;
  margin: auto;
  overflow: auto;
  padding: .25em;
  position: fixed;
  width: fit-content;
  z-index: 2147483647;
}

.github-markdown-body [popover][anchor] {
  inset: auto;
}

.github-markdown-body .pl-c {
  color: var(--color-prettylights-syntax-comment);
}

.github-markdown-body .pl-c1,
.github-markdown-body .pl-s .pl-v {
  color: var(--color-prettylights-syntax-constant);
}

.github-markdown-body .pl-e,
.github-markdown-body .pl-en {
  color: var(--color-prettylights-syntax-entity);
}

.github-markdown-body .pl-smi,
.github-markdown-body .pl-s .pl-s1 {
  color: var(--color-prettylights-syntax-storage-modifier-import);
}

.github-markdown-body .pl-ent {
  color: var(--color-prettylights-syntax-entity-tag);
}

.github-markdown-body .pl-k {
  color: var(--color-prettylights-syntax-keyword);
}

.github-markdown-body .pl-s,
.github-markdown-body .pl-pds,
.github-markdown-body .pl-s .pl-pse .pl-s1,
.github-markdown-body .pl-sr,
.github-markdown-body .pl-sr .pl-cce,
.github-markdown-body .pl-sr .pl-sre,
.github-markdown-body .pl-sr .pl-sra {
  color: var(--color-prettylights-syntax-string);
}

.github-markdown-body .pl-v,
.github-markdown-body .pl-smw {
  color: var(--color-prettylights-syntax-variable);
}

.github-markdown-body .pl-bu {
  color: var(--color-prettylights-syntax-brackethighlighter-unmatched);
}

.github-markdown-body .pl-ii {
  color: var(--color-prettylights-syntax-invalid-illegal-text);
  background-color: var(--color-prettylights-syntax-invalid-illegal-bg);
}

.github-markdown-body .pl-c2 {
  color: var(--color-prettylights-syntax-carriage-return-text);
  background-color: var(--color-prettylights-syntax-carriage-return-bg);
}

.github-markdown-body .pl-sr .pl-cce {
  font-weight: bold;
  color: var(--color-prettylights-syntax-string-regexp);
}

.github-markdown-body .pl-ml {
  color: var(--color-prettylights-syntax-markup-list);
}

.github-markdown-body .pl-mh,
.github-markdown-body .pl-mh .pl-en,
.github-markdown-body .pl-ms {
  font-weight: bold;
  color: var(--color-prettylights-syntax-markup-heading);
}

.github-markdown-body .pl-mi {
  font-style: italic;
  color: var(--color-prettylights-syntax-markup-italic);
}

.github-markdown-body .pl-mb {
  font-weight: bold;
  color: var(--color-prettylights-syntax-markup-bold);
}

.github-markdown-body .pl-md {
  color: var(--color-prettylights-syntax-markup-deleted-text);
  background-color: var(--color-prettylights-syntax-markup-deleted-bg);
}

.github-markdown-body .pl-mi1 {
  color: var(--color-prettylights-syntax-markup-inserted-text);
  background-color: var(--color-prettylights-syntax-markup-inserted-bg);
}

.github-markdown-body .pl-mc {
  color: var(--color-prettylights-syntax-markup-changed-text);
  background-color: var(--color-prettylights-syntax-markup-changed-bg);
}

.github-markdown-body .pl-mi2 {
  color: var(--color-prettylights-syntax-markup-ignored-text);
  background-color: var(--color-prettylights-syntax-markup-ignored-bg);
}

.github-markdown-body .pl-mdr {
  font-weight: bold;
  color: var(--color-prettylights-syntax-meta-diff-range);
}

.github-markdown-body .pl-ba {
  color: var(--color-prettylights-syntax-brackethighlighter-angle);
}

.github-markdown-body .pl-sg {
  color: var(--color-prettylights-syntax-sublimelinter-gutter-mark);
}

.github-markdown-body .pl-corl {
  text-decoration: underline;
  color: var(--color-prettylights-syntax-constant-other-reference-link);
}

.github-markdown-body g-emoji {
  display: inline-block;
  min-width: 1ch;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1em;
  font-style: normal !important;
  font-weight: var(--base-text-weight-normal, 400);
  line-height: 1;
  vertical-align: -0.075em;
}

.github-markdown-body g-emoji img {
  width: 1em;
  height: 1em;
}

.github-markdown-body .task-list-item {
  list-style-type: none;
}

.github-markdown-body .task-list-item label {
  font-weight: var(--base-text-weight-normal, 400);
}

.github-markdown-body .task-list-item.enabled label {
  cursor: pointer;
}

.github-markdown-body .task-list-item + .task-list-item {
  margin-top: 4px;
}

.github-markdown-body .task-list-item .handle {
  display: none;
}

.github-markdown-body .task-list-item-checkbox {
  margin: 0 .2em .25em -1.4em;
  vertical-align: middle;
}

.github-markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {
  margin: 0 -1.6em .25em .2em;
}

.github-markdown-body .contains-task-list {
  position: relative;
}

.github-markdown-body .contains-task-list:hover .task-list-item-convert-container,
.github-markdown-body .contains-task-list:focus-within .task-list-item-convert-container {
  display: block;
  width: auto;
  height: 24px;
  overflow: visible;
  clip: auto;
}

.github-markdown-body .QueryBuilder .qb-entity {
  color: var(--color-prettylights-syntax-entity);
}

.github-markdown-body .QueryBuilder .qb-constant {
  color: var(--color-prettylights-syntax-constant);
}

.github-markdown-body ::-webkit-calendar-picker-indicator {
  filter: invert(50%);
}
