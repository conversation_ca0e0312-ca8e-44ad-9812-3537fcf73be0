<template>
  <div class="flex flex-col gap-16 h-full">
    <div class="flex gap-16 items-center">
      <!-- 行业下拉选择 -->
      <el-select
        v-model="industry"
        clearable
        filterable
        placeholder="请选择行业"
        style="width: 200px;"
        @change="onSearch"
      >
        <el-option v-for="item in smEntityIndustryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <!-- 搜索框 -->
      <el-input
        v-model="searchKeyword"
        class="mr-auto"
        clearable
        placeholder="请输入实体名称"
        :prefix-icon="Search"
        style="width: 240px;"
        @change="onSearch"
      />
      <el-link :icon="Download" type="primary" :underline="false" @click="downloadTemplate">下载模板</el-link>
      <el-upload
        accept=".xlsx"
        :disabled="uploadLoading"
        :http-request="onBatchUpload"
        :show-file-list="false"
      >
        <el-button :loading="uploadLoading">导入</el-button>
      </el-upload>
    </div>
    <DataTable
      class="flex-1"
      :columns="columns"
      :show-index-column="false"
      :status="tableStatus"
      :table-data="tableData"
    />

    <!-- 编辑弹窗 -->
    <EditEntityIndustryModal
      v-model:visible="editModalVisible"
      :entity-data="currentEditEntity"
      @confirm="onEditConfirm"
    />
  </div>
</template>

<script setup lang="tsx">
import EditEntityIndustryModal from './components/EditEntityIndustryModal.vue'
import type { UploadRequestOptions } from 'element-plus'
import { ElButton, ElPopconfirm } from 'element-plus'
import type { Column } from '@/components/DataTable/DataTable.vue'
import { Download, Search } from '@element-plus/icons-vue'
import { useData } from './hooks/useData'
import type { EntityIndustryMapping } from './hooks/useData'
import { useAccountStore } from '@/stores/account'

const {
  uploadEntityIndustryMapping,
  getEntityIndustryMappingList,
  editEntityIndustryMapping,
  deleteEntityIndustryMapping,
} = useData()
const { smEntityIndustryOptions } = storeToRefs(useAccountStore())

const searchKeyword = ref('')
const industry = ref()

const tableStatus = ref<DataStatus>('normal')
const tableData = ref<EntityIndustryMapping[]>([])

// 编辑弹窗相关状态
const editModalVisible = ref(false)
const currentEditEntity = ref<any>({})

const columns: Column<EntityIndustryMapping>[] = [
  {
    prop: 'id',
    label: 'ID',
    width: 80,
  },
  {
    prop: 'entity_name',
    label: '实体名称',
    minWidth: 120,
  },
  {
    prop: 'industry_name',
    label: '行业名称',
    minWidth: 120,
  },
  {
    prop: 'updated_at',
    label: '最新操作时间',
    width: 180,
  },
  {
    prop: 'updated_by',
    label: '最新操作人',
  },
  {
    prop: 'operation',
    label: '操作',
    minWidth: 100,
    fixed: 'right',
    customRenderer: (scope) => {
      return (
        <div class="flex">
          <ElButton type='primary' size='small' text onClick={() => onEdit(scope.row)}>编辑</ElButton>
          <ElPopconfirm
            title="确认删除该记录吗？"
            width="200"
            onConfirm={() => onDelete(scope.row)}
            v-slots={{
              reference: () => (
                <ElButton type='info' size='small' text>删除</ElButton>
              ),
            }}
          >
          </ElPopconfirm>
        </div>
      )
    },
  },
]

async function loadData() {
  if (!searchKeyword.value.trim() && industry.value === undefined) {
    tableStatus.value = 'normal'
    tableData.value = []
    return
  }
  tableStatus.value = 'loading'
  const { data } = await getEntityIndustryMappingList({
    industry_code: industry.value,
    searchValue: searchKeyword.value.trim(),
  })
  tableData.value = data
  tableStatus.value = data.length ? 'normal' : 'empty'
}

function onSearch() {
  loadData()
}

const uploadLoading = ref(false)
async function onBatchUpload(options: UploadRequestOptions) {
  try {
    uploadLoading.value = true
    const { error_code, error_msg } = await uploadEntityIndustryMapping(options.file)
    if (error_code !== 0) {
      ElMessage.error(error_msg)
      return
    }
    ElMessage.success('导入成功')
    loadData()
  }
  catch (e) {
    ElMessage.error('上传失败')
  }
  finally {
    uploadLoading.value = false
  }
}

// 处理编辑按钮点击事件
function onEdit(row: EntityIndustryMapping) {
  currentEditEntity.value = row
  editModalVisible.value = true
}

// 处理删除按钮点击事件
async function onDelete(row: EntityIndustryMapping) {
  const { error_code, error_msg } = await deleteEntityIndustryMapping(row.id)
  if (error_code === 0) {
    ElMessage.success('删除成功')
    await loadData()
  }
  else {
    ElMessage.error(`删除失败，原因：${error_msg}`)
  }
}

// 处理编辑确认事件
async function onEditConfirm(formData: Partial<EntityIndustryMapping>) {
  const { id, entity_name, industry_name } = formData
  if (!id || !entity_name?.trim() || !industry_name?.trim()) {
    ElMessage.error('请输入完整信息')
    return
  }
  const { error_msg, error_code } = await editEntityIndustryMapping({
    id,
    entity_name: entity_name?.trim() || '',
    industry_name: industry_name?.trim() || '',
  })
  if (error_code === 0) {
    ElMessage.success('编辑成功')
    editModalVisible.value = false
    loadData()
  }
  else {
    ElMessage.error(`编辑失败，原因：${error_msg}`)
  }
}

// 下载模板
async function downloadTemplate() {
  const url = 'https://osscdn.datastory.com.cn/creative/static/resource/28a521c7b427a17e2c77da018d8fd6e4.xlsx'
  const filename = '社媒实体行业映射表批量上传模板.xlsx'

  try {
    const response = await fetch(url)
    const blob = await response.blob()

    const objectUrl = window.URL.createObjectURL(new Blob([blob], {
      type: 'application/octet-stream',
    }))

    const link = document.createElement('a')
    link.href = objectUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(objectUrl)
  }
  catch (error) {
    console.error('下载模板失败:', error)
  }
}
</script>
