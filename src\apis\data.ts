import { request } from '@/utils/request'

export interface DataRequestQuery {
  methodName?: string
  order?: object
  limit?: number
  offset?: number
  filters?: object[]
  trim?: boolean
}

export interface DataRequest {
  query?: DataRequestQuery
}

export interface LoadResponse<T = any> {
  data: T
  total?: number
}

export interface CommonResponse<T = any> {
  data: T
  total: number | null
  code: number
  message: string
  headers?: string[] | null
}

export interface ActionRequestV2 {
  methodName: string
  params: Record<string, any>
}

export function loadV1<T = any>(data: DataRequest): Promise<LoadResponse<T>> {
  return request({
    url: 'data/v1/load',
    method: 'post',
    data,
  })
}

export function loadV2<T = any>(data: DataRequest): Promise<CommonResponse<T>> {
  let url = '/data/v2/load'
  if (data.query) {
    url = `${url}/${data.query.methodName}`
    delete data.query.methodName
  }

  return request({
    url,
    method: 'post',
    data,
  })
}

export function pageApiV2<T = any>(data: DataRequest): Promise<CommonResponse<T>> {
  let url = '/data/v2/page'
  if (data.query) {
    url = `${url}/${data.query.methodName}`
    delete data.query.methodName
  }

  return request({
    url,
    method: 'post',
    data,
  })
}

export function actionApiV2<T = any>(data: ActionRequestV2): Promise<CommonResponse<T>> {
  const { methodName, params } = data
  return request({
    url: `ops/v2/action/${methodName}`,
    method: 'post',
    data: { params },
  })
}

export function miscApi<T = any>(data: DataRequest): Promise<CommonResponse<T>> {
  const extraConfig = {}

  let url = '/data/v2/misc'
  if (data.query) {
    url = `${url}/${data.query.methodName}`
    delete data.query.methodName
  }

  return request({
    url,
    method: 'post',
    data,
    ...extraConfig,
  })
}
