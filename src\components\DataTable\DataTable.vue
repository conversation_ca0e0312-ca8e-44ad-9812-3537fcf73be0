<template>
  <div ref="tableContainer" v-loading="status === 'loading'" class="flex flex-col gap-16">
    <el-table
      :data="tableData"
      :height="height"
      :row-key="rowKey"
    >
      <el-table-column v-if="showIndexColumn" label="序号" type="index" width="60" />
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :align="column.align"
        :fixed="column.fixed"
        :formatter="column.formatter"
        :label="column.label"
        :min-width="column.minWidth"
        :prop="column.prop"
        :show-overflow-tooltip="column.showOverflowTooltip"
        :type="column.type"
        :width="column.width"
      >
        <template v-if="column.customRenderer" #default="scope">
          <Renderer :renderer="column.customRenderer" :scope="scope" />
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="pageOption"
      background
      class="justify-end"
      :current-page="pageOption.currentPage"
      layout="total, prev, pager, next"
      :page-size="pageOption.pageSize"
      :total="pageOption.total"
      @current-change="onPageChange"
    />
  </div>
</template>

<script setup lang="ts" generic="T">
import type { TableColumnCtx } from 'element-plus'
import type { VNode } from 'vue'
import Renderer from './Renderer.vue'

export interface PageOption {
  currentPage: number
  pageSize: number
  total: number
}
export interface Column<T> extends Partial<TableColumnCtx<T>> {
  customRenderer?: (scope: any) => VNode
}

const props = withDefaults(defineProps<{
  tableData: T[]
  columns: Column<T>[]
  pageOption?: PageOption // 分页选项
  showIndexColumn?: boolean
  status?: DataStatus
  rowKey?: string
}>(), {
  showIndexColumn: true,
})
const emit = defineEmits<{
  (e: 'update:page-option', pageOption: PageOption): void
  (e: 'pageChange'): void
}>()

const tableContainer = ref<HTMLElement | null>(null)
const tableHeight = ref(400)
const height = computed(() => {
  return tableHeight.value
})

onMounted(() => {
  updateTableHeight()
  window.addEventListener('resize', updateTableHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateTableHeight)
})

function updateTableHeight() {
  if (tableContainer.value) {
    // 获取容器的高度
    const containerHeight = tableContainer.value?.clientHeight || 0
    // 如果有分页，需要减去分页的高度（大约 40px）和间距（16px）
    const paginationHeight = props.pageOption ? 56 : 0
    tableHeight.value = containerHeight - paginationHeight
  }
}

function onPageChange(page: number) {
  if (!props.pageOption) return
  emit('update:page-option',
    {
      currentPage: page,
      pageSize: props.pageOption.pageSize,
      total: props.pageOption.total,
    },
  )
  emit('pageChange')
}
</script>

<style scoped>

</style>
