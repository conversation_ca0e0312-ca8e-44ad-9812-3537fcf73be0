<template>
  <div class="flex flex-col gap-16 h-full w-full">
    <el-menu
      :default-active="activeMenuName"
      mode="horizontal"
    >
      <template v-for="child in topLevelRoutes" :key="child.name">
        <!-- 如果有子路由，则使用 SubMenu -->
        <el-sub-menu v-if="child.children && child.children.length > 0" :index="child.name">
          <template #title>{{ child.meta?.title || child.name }}</template>
          <el-menu-item
            v-for="subChild in child.children"
            :key="subChild.name"
            :index="subChild.name"
            @click="$router.push({ name: subChild.name })"
          >
            {{ subChild.meta?.title || subChild.name }}
          </el-menu-item>
        </el-sub-menu>
        <!-- 如果没有子路由，则使用普通 MenuItem -->
        <el-menu-item
          v-else
          :index="child.name"
          @click="$router.push({ name: child.name })"
        >
          {{ child.meta?.title || child.name }}
        </el-menu-item>
      </template>
    </el-menu>
    <RouterView class="flex-1" />
  </div>
</template>

<script setup lang="ts">
const route = useRoute()

// 获取顶层路由
const topLevelRoutes = computed(() => {
  if (route.matched && route.matched.length > 0) {
    return route.matched[0].children || []
  }
  return []
})

// 计算当前激活的菜单项
const activeMenuName = computed(() => {
  return route.name
})
</script>

<style scoped>

</style>
