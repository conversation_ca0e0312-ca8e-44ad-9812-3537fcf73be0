<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="username">
      <el-input v-model="loginForm.username" placeholder="用户名">
        <template #prefix>
          <el-icon class="el-input__icon">
            <User />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input v-model="loginForm.password" autocomplete="new-password" placeholder="密码" show-password type="password">
        <template #prefix>
          <el-icon class="el-input__icon">
            <Lock />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
  <div class="login-btn">
    <el-button :icon="CircleClose" size="large" @click="resetForm(loginFormRef)"> 重置 </el-button>
    <el-button :icon="UserFilled" :loading="loading" size="large" type="primary" @click="login(loginFormRef)">
      登录
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { HOME_URL } from '@/config'
import type { LoginRequestData } from '@/apis/auth'
import { ElForm } from 'element-plus'
import { login as loginApi } from '@/apis/auth'
import { useAccountStore } from '@/stores/account'
import { CircleClose, Lock, User, UserFilled } from '@element-plus/icons-vue'
import { setToken } from '@/utils/cookies'

const router = useRouter()
const accountStore = useAccountStore()

type FormInstance = InstanceType<typeof ElForm>
const loginFormRef = ref<FormInstance>()
const loginRules = reactive({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

const loading = ref(false)
const loginForm = reactive<LoginRequestData>({
  username: '',
  password: '',
  code: '',
})

// login
function login(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (!valid) return
    loading.value = true
    try {
      // 调用登录接口
      const { data, code, message } = await loginApi({ ...loginForm })
      if (code === 20000) {
        accountStore.updateUserInfo(data)
        setToken(data.token)
        // 跳转到首页
        router.push(HOME_URL)
      }
      else {
        let errorMsg = ''
        switch (code) {
          case 50003:
            errorMsg = '该账号不存在'
            break
          case 50004:
          case 50000:
            errorMsg = message
            break
          case 50008:
            errorMsg = '当前使用的登录方式与账号不匹配'
            break
          case 50009:
            // 账号未绑定过设备
            errorMsg = '账号未绑定过设备'
            break
          case 50010:
            // 设备id不匹配
            errorMsg = '账号已有绑定设备，请使用已绑设备初始环境进行登录'
            break
          default:
            errorMsg = '登录失败，请检查账号密码是否正确'
            break
        }
        ElMessage.error(errorMsg)
      }
    }
    catch (err) {
      console.log('登录失败', err)
      ElMessage.error('登录失败')
    }
    finally {
      loading.value = false
    }
  })
}

// resetForm
function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
}

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e
    if (e.code === 'Enter' || e.code === 'enter' || e.code === 'NumpadEnter') {
      if (loading.value) return
      login(loginFormRef.value)
    }
  }
})
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
