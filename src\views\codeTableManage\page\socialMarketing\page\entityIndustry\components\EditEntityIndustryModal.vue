<template>
  <el-dialog
    destroy-on-close
    :model-value="visible"
    title="编辑实体码表"
    width="600px"
    @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="p-16">
      <el-form label-position="left" label-width="100px" :model="form">
        <el-form-item label="ID">
          <div>{{ form.id }}</div>
        </el-form-item>
        <el-form-item label="实体名称">
          <el-input v-model="form.entity_name" />
        </el-form-item>
        <el-form-item label="行业名称">
          <el-select v-model="form.industry_code" filterable>
            <el-option
              v-for="item in smEntityIndustryOptions"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex gap-16 justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { EntityIndustryMapping } from '../hooks/useData'
import { useAccountStore } from '@/stores/account'

const props = defineProps<{
  visible: boolean
  entityData: EntityIndustryMapping
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', formData: Partial<EntityIndustryMapping>): void
}>()

const { smEntityIndustryOptions } = storeToRefs(useAccountStore())

const form = ref<Partial<EntityIndustryMapping>>({
  entity_code: 0,
  entity_name: '',
  industry_code: 0,
  industry_name: '',
  id: 0,
})

// 监听 activityData 变化，更新表单数据
watch(() => props.visible, () => {
  if (props.visible) {
    form.value = cloneDeep({
      ...props.entityData,
    })
  }
}, { immediate: true, deep: true })

function handleClose() {
  emit('update:visible', false)
}

function handleConfirm() {
  emit('confirm', cloneDeep({
    ...form.value,
    industry_name: smEntityIndustryOptions.value.find(item => item.value === form.value.industry_code)?.label || '',
  }))
}
</script>

<style scoped>
.p-16 {
  padding: 16px;
}
.border-b {
  border-bottom: 1px solid #ebeef5;
}
</style>
