import { ALL_METRIC_INFO_LIST, ALL_MODULE_INFO_LIST, ALL_TARGET_INFO_LIST, DATE_TYPE_MAP } from '@/constants/option'
import { ElTag } from 'element-plus'
import { formatNumber, formatPercent } from '@/utils/data'
import type { Column } from '@/components/DataTable/DataTable.vue'

interface Option {
  menu: string
}

export function useColumns<T extends Record<string, any>>(data: T, option: Option) {
  const allColumns: Column<T>[] = [
    {
      prop: 'inspection_module',
      label: '模块',
      formatter: (row: any) => {
        const moduleInfo = ALL_MODULE_INFO_LIST.find(item => item.value === row.inspection_module)
        return moduleInfo?.label || 'N/A'
      },
    },
    {
      prop: 'inspection_object',
      label: '对象级别',
      formatter: (row: any) => {
        const targetInfo = ALL_TARGET_INFO_LIST.find(item => item.value === row.inspection_object)
        return targetInfo?.label || 'N/A'
      },
    },
    {
      prop: 'region',
      label: '地域',
      formatter: (row: any) => {
        const map = {
          china: '中国',
          overseas: '海外',
          global: '全球',
        }
        return map[row.region as keyof typeof map] || 'N/A'
      },
    },
    {
      prop: 'inspection_index',
      label: '分析指标',
      minWidth: 150,
      formatter: (row: any) => { // for download
        if (!row.inspection_index) return 'N/A'
        const metrics = row.inspection_index.split(',')
        const metricList = metrics.map((metric: string) => {
          if (metric.startsWith('pop_')) {
            const realMetric = metric.replace('pop_', '')
            const metricInfo = ALL_METRIC_INFO_LIST.find((item: any) => item.value === realMetric)
            return `${metricInfo?.label}【环比】` || 'N/A'
          }
          else if (metric.startsWith('yoy_')) {
            const realMetric = metric.replace('yoy_', '')
            const metricInfo = ALL_METRIC_INFO_LIST.find((item: any) => item.value === realMetric)
            return `${metricInfo?.label}【同比】` || 'N/A'
          }
          else {
            const metricInfo = ALL_METRIC_INFO_LIST.find((item: any) => item.value === metric)
            return metricInfo?.label || 'N/A'
          }
        })
        return metricList.join(',')
      },
      customRenderer: ({ row }: { row: any }) => {
        if (!row.inspection_index) return <div>N/A</div>
        const metrics = row.inspection_index.split(',')
        const metricList = metrics.map((metric: string) => {
          if (metric.startsWith('pop_')) {
            const realMetric = metric.replace('pop_', '')
            const metricInfo = ALL_METRIC_INFO_LIST.find((item: any) => item.value === realMetric)
            return `${metricInfo?.label}【环比】` || 'N/A'
          }
          else if (metric.startsWith('yoy_')) {
            const realMetric = metric.replace('yoy_', '')
            const metricInfo = ALL_METRIC_INFO_LIST.find((item: any) => item.value === realMetric)
            return `${metricInfo?.label}【同比】` || 'N/A'
          }
          else {
            const metricInfo = ALL_METRIC_INFO_LIST.find((item: any) => item.value === metric)
            return metricInfo?.label || 'N/A'
          }
        })
        return <div class="flex flex-wrap gap-4">
          {metricList.map((metric: string) => (
            <ElTag>{metric}</ElTag>
          ))}
        </div>
      },

    },
    {
      prop: 'date_type',
      label: '时间周期',
      formatter: (row: any) => {
        return DATE_TYPE_MAP[row.date_type]
      },
    },
    {
      prop: 'entity_name',
      label: '对象名称',
    },
    {
      prop: 'l1_industry_name',
      label: '一级行业',
    },
    {
      prop: 'l1_industry_code',
      label: '一级行业code',
      formatter: (row: any) => {
        return row.l1_industry_code.toString()
      },
    },
    {
      prop: 'l2_industry_name',
      label: '二级行业',
    },
    {
      prop: 'l2_industry_code',
      label: '二级行业code',
      formatter: (row: any) => {
        return row.l2_industry_code.toString()
      },
    },
    {
      prop: 'entity_type',
      label: '产品类型',
    },
    {
      prop: 'event_date',
      label: '时间',
    },
    {
      prop: 'index_value',
      label: '值',
      formatter: (row: any) => {
        const value = row.index_value
        const metric = row.inspection_index?.split(',')[0]
        if (metric?.startsWith('pop_') || metric?.startsWith('yoy_')) {
          return formatPercent(value)
        }
        return value
      },
    },
    {
      prop: 'duplicate_times',
      label: '重复次数',
    },
    {
      prop: 'entity_code',
      label: 'code',
      formatter: (row: any) => {
        return row.entity_code.toString()
      },
    },
    {
      prop: 'prev_original_index_value',
      label: '上期原始指标值',
      formatter: (row: any) => {
        const value = row.prev_original_index_value
        return formatNumber(value)
      },
    },
    {
      prop: 'current_original_index_value',
      label: '当期原始指标值',
      formatter: (row: any) => {
        const value = row.current_original_index_value
        return formatNumber(value)
      },
    },
  ]

  const dataKeys = Object.keys(data)
  const columns = allColumns.filter(item => dataKeys.includes(item.prop || ''))

  // 针对entity_code，不同菜单显示不同的code
  const codeColumn = columns.find(item => item.prop === 'entity_code')
  if (codeColumn) {
    codeColumn.label = option.menu === 'ai_holo' ? 'ai_code' : 'app_code'
  }

  return {
    columns,
  }
}
