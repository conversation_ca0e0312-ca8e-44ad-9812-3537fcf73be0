import {
  defineConfig,
  presetAttributify,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import presetCustomRem from './preset-custom-rem'

const colors = {
  'bg-white': '#ffffff',
  'bg-black': '#141414',
  'white': '#f8f8f7',
  'black': '#000000',
  'primary': '#f24c42',
  'success': '#afe3cb',
  'warning': '#ffbf5f',
  'danger': '#ff0000',
  'error': '#ff0000',
  'info': '#a0b5ec',
}

export default defineConfig({
  theme: {
    colors,
  },
  shortcuts: [
    ['bg-default', 'bg-[var(--el-mask-color)]'],
  ],
  presets: [
    presetUno(),
    presetAttributify(),
    presetCustomRem(),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  rules: [
    [
      'flex-center',
      {
        'display': 'flex',
        'justify-content': 'center',
        'align-items': 'center',
      },
    ],
    [
      'ellipsis',
      {
        'text-overflow': 'ellipsis',
        'overflow': 'hidden',
        'white-space': 'nowrap',
      },
    ],
  ],
})
