export interface StepComponentProps {
  step: number
  stepData?: any
  processData?: any
}

// 分拣流程状态
export type TaskStatus = 'processing' | 'finished'
// 分拣流程基本信息
export interface Task {
  task_id: number
  task_name: string
  task_status: TaskStatus
  created_at: string
  created_by: number
  updated_at: string
  updated_by: number
}

// 分拣流程步骤状态
export type StepStatus = 'processing' | 'finished' | 'failed'
// 分拣流程步骤信息
export interface TaskStep {
  step_id: number
  task_id: number
  step_type: number
  step_status: StepStatus
  quantity: number
  oss_url?: string
  created_at: string
  created_by: number
  updated_at: string
  updated_by: number
}

// 步骤 2 返回的标记了实体状态的数据
export interface TaskStepCheckStatusData {
  name: string
  domain: string
  auto_matching_status: number
  discover_time: string
  final_status?: number
  web_id?: number
  web_name?: string
}

// 步骤 6 返回的待补全的数据
export interface WebInfo {
  web_id: number
  name: string
  domain: string
  company_fname: string
  company_sname: string
  group_company: null
  is_ai: boolean
  region: string
  is_export: boolean
  release_time: string
  discover_time: string
  scale: number
  is_scale_standard: boolean
  industry_l1_code_ai_label: string
  industry_l1_code: null
  logo_url: string
  is_collect: boolean
  collect_time: string
  collect_status: number
  introduction: string
  remark: string
  created_at: string
  updated_at: string
}

// 步骤 7 返回的待映射的web列表
export interface TaskStepMatchData {
  web_id: number
  web_name: string
  app_id: number
  app_name: string
}

export interface TaskListParams {
  offset?: number
  limit?: number
  task_status?: StepStatus
  task_id?: number
}

export interface AiInfo {
  app_id: number
  name: string
}

export interface AiIndustry {
  id: string
  industry_code: number
  industry_name: string
  industry_level: number
  industry_parent: number
  sort: number
  created_at: string
  updated_at: string
  industry_en_name: string
  au: number
}
