import type { AiIndustry, AiInfo } from './type'

function _useFilter() {
  const taskId = ref(0)

  // 当前流程所在步骤 0 为第一步
  const currentStep = ref(0)

  // 分拣类型
  const entityTypeOptions = [
    { value: 'ai_web', label: 'AI WEB' },
    { value: 'ai_app', label: 'AI APP', disabled: true },
  ]
  const entityType = ref<('ai_web' | 'ai_app')>('ai_web')

  const allAiList = ref<AiInfo[]>([])
  const allAiIndustryList = ref<AiIndustry[]>([])

  return {
    taskId,
    currentStep,
    entityType,
    entityTypeOptions,
    allAiList,
    allAiIndustryList,
  }
}

export const useFilter = createSharedComposable(_useFilter)
