module.exports = {
  extends: [
    '@antfu/eslint-config-vue', // https://antfu.me/posts/why-not-prettier-zh
    './.eslintrc-auto-import.json',
  ],
  overrides: [{
    files: ['*.ts', '*.tsx', '*.vue'],
    rules: {
      '@typescript-eslint/comma-dangle': 'off',
      '@typescript-eslint/no-unused-vars': ['warn'],
      'import/order': 0,
      // Eslint
      'comma-dangle': ['warn', 'always-multiline'],
      'no-debugger': 0,
      'no-unused-vars': 'off',
      'no-console': 'off',
      'multiline-ternary': 0,
      'curly': 0,
      // AntFu
      'antfu/if-newline': 'off',
      // Vue
      'vue/no-extra-parens': 'off',
      'vue/singleline-html-element-content-newline': 'off',
      'vue/component-name-in-template-casing': [
        'warn', 'PascalCase',
        { registeredComponentsOnly: false, ignores: ['/^el-/'] },
      ],
      'vue/quote-props': 'off',
      'vue/static-class-names-order': ['warn'],
      'vue/component-tags-order': ['error', {
        order: [['script', 'template'], 'style'],
      }],
      'vue/attributes-order': ['warn', { alphabetical: true }],
    },
  }],
}
