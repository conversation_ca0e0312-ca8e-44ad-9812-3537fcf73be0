<template>
  <header>
    <div class="user-info">
      <!-- <el-avatar class="user-avatar">
        {{ userInfo?.user_name.charAt(0) }}
      </el-avatar> -->
      <div class="user-details">
        <div class="user-name">
          {{ userInfo?.user_name }}
        </div>
        <div class="date">
          {{ dayjs().format('ddd, MMM DD, YYYY') }}
        </div>
      </div>
    </div>
    <div class="actions-panel">
      <div class="action-button" @click="toggleDark()">
        <IEpSunny v-show="!isDark" />
        <IEpMoon v-show="isDark" />
      </div>
      <el-dropdown>
        <div class="action-button">
          <IEpSetting />
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="logoutAndClear">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup lang="ts">
import { useAccountStore } from '@/stores/account'
import dayjs from 'dayjs'
import { isDark, toggleDark } from '@/utils/dark'

const { userInfo } = toRefs(useAccountStore())
const { logoutAndClear } = useAccountStore()
</script>

<style scoped>
header {
  @apply w-full h-48 flex items-center justify-between mt-24;
}
.user-info {
  @apply flex items-center gap-16;
}
.user-avatar {
  @apply w-48 h-48 text-20 bg-primary;
}
.user-details {
  @apply flex flex-col gap-4;
}
.user-name {
  @apply text-20;
}
.date {
  @apply text-14 text-black/50 dark:text-white/50;
}
.actions-panel {
  @apply flex items-center gap-8;
}
.action-button {
  @apply flex-center w-48 h-48 b-rd-50% bg-bg-white dark:bg-bg-black cursor-pointer;
}
</style>
