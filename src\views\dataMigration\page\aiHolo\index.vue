<template>
  <div class="flex flex-col">
    <div class="my-12">
      <el-segmented v-model="tab" :options="tabOptions" size="large" />
    </div>
    <TimeForm v-if="tab === '按时间迁移'" class="flex-1" />
    <AppForm v-if="tab === '按应用迁移'" class="flex-1" />
  </div>
</template>

<script setup lang="ts">
import TimeForm from './components/TimeForm.vue'
import AppForm from './components/AppForm.vue'

const tab = ref('按时间迁移')
const tabOptions = ['按时间迁移', '按应用迁移']
</script>

<style scoped></style>
