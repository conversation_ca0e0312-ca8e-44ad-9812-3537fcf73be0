<template>
  <div class="flex flex-1 flex-col gap-16">
    <div class="flex gap-16">
      <!-- 行业筛选 -->
      <el-cascader
        v-model="industryCodeList"
        filterable
        :options="industryOptions"
        :props="{
          expandTrigger: 'hover',
          label: 'industry_name',
          value: 'industry_code',
          checkStrictly: true,
        }"
        @change="onIndustryChange"
      />
      <!-- 产品是否启用状态筛选 -->
      <el-select v-model="enabledStatus" clearable placeholder="请筛选产品是否启用" style="width: 180px" @change="loadData">
        <el-option label="启用" value="1" />
        <el-option label="未启用" value="0" />
      </el-select>
      <el-link class="ml-auto" :icon="Download" type="primary" :underline="false" @click="downloadTemplate">下载模板</el-link>
      <el-upload
        accept=".xlsx"
        :disabled="uploadLoading"
        :http-request="onBatchUpload"
        :show-file-list="false"
      >
        <el-button :loading="uploadLoading">导入Prompt</el-button>
      </el-upload>
    </div>
    <DataTable
      class="flex-1"
      :columns="columns"
      :show-index-column="false"
      :status="tableStatus"
      :table-data="tableData"
    />
    <EditPromptModal
      v-model:visible="editPromptModalVisible"
      :data="editPromptData"
      :industry-options="industryOptions"
      @confirm="onEditPromptConfirm"
    />
  </div>
</template>

<script setup lang="tsx">
import type { UploadRequestOptions } from 'element-plus'
import { ElButton } from 'element-plus'
import type { Column } from '@/components/DataTable/DataTable.vue'
import type { IndustryOptions, Prompt, PromptIndustry } from '../hooks/useData'
import { Download } from '@element-plus/icons-vue'
import { useData } from '../hooks/useData'
import EditPromptModal from './EditPromptModal.vue'

const {
  getPromptList,
  updatePrompt,
  uploadPrompt,
  getPromptIndustryList,
} = useData()

const industryOptions = ref<IndustryOptions[]>([])
const industryCodeList = ref<string[]>([])
const industryList = ref<PromptIndustry[]>([])
const enabledStatus = ref<string | null>(null)

function onIndustryChange() {
  loadData()
}

const tableStatus = ref<DataStatus>('normal')
const tableData = ref<Prompt[]>([])

const columns: Column<Prompt>[] = [
  {
    prop: 'question_id',
    label: 'Question ID',
    width: 120,
  },
  {
    prop: 'prompt',
    label: 'Prompt',
    minWidth: 120,
  },
  {
    prop: 'industry_code',
    label: '所属行业',
    formatter: (row) => {
      const industry = industryList.value.find(item => item.industry_code === row.industry_code)
      if (industry) {
        if (industry.industry_level === 1) {
          return industry.industry_name
        }
        else {
          const parentIndustry = industryList.value.find(item => item.industry_code === industry.industry_parent)
          return `${parentIndustry?.industry_name} > ${industry.industry_name}`
        }
      }
      return '-'
    },
  },
  {
    prop: 'source',
    label: '来源',
    width: 60,
  },
  {
    prop: 'user_code',
    label: '所属用户编码',
    width: 120,
    align: 'center',
    formatter: (row) => {
      return row.user_code?.toString() || '-'
    },
  },
  {
    prop: 'enabled',
    label: '产品是否启用',
    width: 120,
    align: 'center',
    customRenderer: (scope) => {
      return (
        <el-switch v-model={scope.row.enabled} size="small" style="--el-switch-on-color: #049541;" onChange={() => onUpdateEnabled(scope.row)} />
      )
    },
  },
  {
    prop: 'collect_status',
    label: '采集状态',
    width: 100,
    align: 'center',
    customRenderer: (scope) => {
      const collectStatusMap: Record<number, string> = {
        0: '不采集',
        1: '常规采集',
        2: '优先采集',
      }
      const tagType = ['info', 'success', 'primary'][scope.row.collect_status]
      return (
        <el-tag type={tagType} effect="plain" size="small">{collectStatusMap[scope.row.collect_status]}</el-tag>
      )
    },
  },
  {
    prop: 'updated_at',
    label: '最新操作时间',
    width: 180,
  },
  {
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    customRenderer: (scope) => {
      return (
        <div class="flex">
          <ElButton type='primary' size='small' onClick={() => onOpenEditDialog(scope.row)}>编辑</ElButton>
        </div>
      )
    },
  },
]

// 加载数据
async function loadData() {
  tableStatus.value = 'loading'
  const industryCode: (number | string)[] = []
  if (industryCodeList.value.length === 1) {
    const options = industryOptions.value.find(item => item.industry_code === industryCodeList.value[0])
    if (options) {
      industryCode.push(options.industry_code)
      options.children?.forEach((item) => {
        industryCode.push(item.industry_code)
      })
    }
  }
  else {
    industryCode.push(industryCodeList.value.at(-1) || 0)
  }
  const { data } = await getPromptList({
    industry_code: industryCode,
    ...(typeof enabledStatus.value === 'string' && { enabled: enabledStatus.value === '1' }),
  })
  tableData.value = data.sort((a: Prompt, b: Prompt) => a.question_id - b.question_id)
  tableStatus.value = data.length ? 'normal' : 'empty'
}

const uploadLoading = ref(false)
async function onBatchUpload(options: UploadRequestOptions) {
  try {
    uploadLoading.value = true
    const { error_code, error_msg } = await uploadPrompt(options.file)
    if (error_code !== 0) {
      ElMessage.error(error_msg)
      return
    }
    ElMessage.success('导入成功')
    loadData()
  }
  catch (e) {
    ElMessage.error('上传失败')
  }
  finally {
    uploadLoading.value = false
  }
}

// 处理采集状态更新
async function onUpdateCollectStatus(row: Prompt, collect_status: number) {
  const { error_code, error_msg } = await updatePrompt({
    question_id: row.question_id,
    field_name: 'collect_status',
    field_value: collect_status,
  })
  if (error_code === 0) {
    ElMessage.success('状态更新成功')
    await loadData()
  }
  else {
    ElMessage.error(`状态更新成功，原因：${error_msg}`)
  }
}

// 处理产品是否启用状态更新
async function onUpdateEnabled(row: Prompt) {
  const { error_code, error_msg } = await updatePrompt({
    question_id: row.question_id,
    field_name: 'enabled',
    field_value: row.enabled,
  })
  if (error_code === 0) {
    ElMessage.success('状态更新成功')
    await loadData()
  }
  else {
    ElMessage.error(`状态更新成功，原因：${error_msg}`)
  }
}

const editPromptModalVisible = ref(false)
const editPromptData = ref<Prompt>()

// 处理编辑
function onOpenEditDialog(row: Prompt) {
  editPromptData.value = row
  editPromptModalVisible.value = true
}

async function onEditPromptConfirm(formData: Partial<Prompt>) {
  const { question_id, collect_status, industry_code } = formData
  const { error_code: collect_error_code, error_msg: collect_error_msg } = await updatePrompt({
    question_id: question_id!,
    field_name: 'collect_status',
    field_value: collect_status!,
  })
  const { error_code: industry_error_code, error_msg: industry_error_msg } = await updatePrompt({
    question_id: question_id!,
    field_name: 'industry_code',
    field_value: industry_code!,
  })
  if (collect_error_code === 0 && industry_error_code === 0) {
    ElMessage.success('更新成功')
    editPromptModalVisible.value = false
    await loadData()
  }
  else {
    ElMessage.error(`更新失败，原因：${collect_error_msg} ${industry_error_msg}`)
  }
}

// 下载模板
async function downloadTemplate() {
  const url = 'https://osscdn.datastory.com.cn/creative/static/resource/95e6f9f1578159488a058f3904a8e687.xlsx'
  const filename = 'Prompt导入模板.xlsx'

  try {
    const response = await fetch(url)
    const blob = await response.blob()

    const objectUrl = window.URL.createObjectURL(new Blob([blob], {
      type: 'application/octet-stream',
    }))

    const link = document.createElement('a')
    link.href = objectUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(objectUrl)
  }
  catch (error) {
    console.error('下载模板失败:', error)
  }
}

async function loadPromptIndustryList() {
  const { data, options } = await getPromptIndustryList()
  industryList.value = data
  industryOptions.value = options
  // 默认选择第一个
  industryCodeList.value = [options[0].industry_code]
}

onMounted(async () => {
  // 加载行业
  await loadPromptIndustryList()
  loadData()
})
</script>

<style lang="scss" scoped>
.tip-container {
  @apply flex items-center justify-center h-400;
}
</style>
