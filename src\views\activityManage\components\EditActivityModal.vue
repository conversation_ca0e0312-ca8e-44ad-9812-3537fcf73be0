<template>
  <el-dialog
    destroy-on-close
    :model-value="visible"
    title="编辑活动信息"
    width="600px"
    @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="p-16">
      <el-form label-position="left" label-width="100px" :model="form">
        <el-form-item label="code">
          <div>{{ form.activity_code }}</div>
        </el-form-item>
        <el-form-item label="活动名称">
          <el-input v-model="form.activity_name" />
        </el-form-item>
        <el-form-item label="活动类型">
          <el-select v-model="form.activity_type">
            <el-option
              v-for="item in activitiesTypeOptions"
              :key="item.label"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联对象">
          <el-input v-model="form.related_entity" />
        </el-form-item>
        <el-form-item label="行业">
          <el-cascader
            v-model="form.industry_code_list"
            filterable
            :options="smIndustryOptions"
            :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'industry_code', checkStrictly: true }"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex gap-16 justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useAccountStore } from '@/stores/account'
import { findPathById } from '@/utils/data'
import { cloneDeep } from 'lodash-es'
import type { LibActivity } from '../hooks/useData'

const props = defineProps<{
  visible: boolean
  activityData: {
    activity_code: string
    activity_name: string
    activity_type: string
    related_entity: string
    logo_url: string
    industry_code: string
    start_date: string
    end_date: string
  }
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', formData: Partial<LibActivity>): void
}>()

const { smIndustryOptions } = storeToRefs(useAccountStore())
const activitiesTypeOptions = [
  { label: '品牌营销' },
  { label: '节日营销' },
  { label: '新品发布' },
  { label: '电商大促' },
  { label: '其他' },
] as const

const form = ref({
  activity_code: '',
  activity_name: '',
  activity_type: '',
  related_entity: '',
  logo_url: '',
  industry_code_list: [] as string[],
  start_date: '',
  end_date: '',
})

// 监听 activityData 变化，更新表单数据
watch(() => props.visible, () => {
  if (props.visible) {
    form.value = cloneDeep({
      ...props.activityData,
      industry_code_list: findPathById(
        smIndustryOptions.value,
        'industry_code',
        props.activityData.industry_code?.toString(),
      ) as string[] || [],
    })
  }
}, { immediate: true, deep: true })

function handleClose() {
  emit('update:visible', false)
}

function handleConfirm() {
  emit('confirm', cloneDeep({
    ...form.value,
    industry_code: Number(form.value.industry_code_list.at(-1))!,
  }))
}
</script>

<style scoped>
.p-16 {
  padding: 16px;
}
.border-b {
  border-bottom: 1px solid #ebeef5;
}
</style>
