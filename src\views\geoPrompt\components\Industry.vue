<template>
  <div class="flex flex-1 flex-col gap-16 h-full overflow-hidden">
    <div class="flex gap-16">
      <el-button plain size="small" type="primary" @click="onCollapseAll">收起所有</el-button>
      <el-button plain size="small" type="info" @click="onExpandAll">展开所有</el-button>
    </div>
    <div class="flex-1 overflow-y-auto">
      <el-tree
        ref="industryTreeRef"
        :allow-drop="allowDrop"
        class="flex-1"
        :data="industryOptions"
        draggable
        node-key="industry_code"
        :props="{
          label: 'industry_name',
        }"
        style="--el-tree-node-content-height: 36px;"
        @node-drop="onNodeDrop"
      >
        <template #default="{ data }">
          <div class="flex items-center pl-16 w-full">
            <span>{{ data.industry_name }}</span>
            <el-tag v-if="!data.enabled" class="ml-8" size="small" type="primary">不启用</el-tag>
            <div class="flex gap-8 ml-auto pr-16">
              <el-button link size="small" type="info" @click.stop="onAddSiblingIndustry(data)">新增同级行业</el-button>
              <el-button link size="small" type="primary" @click.stop="onEditIndustry(data)">编辑</el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 新增/编辑行业 -->
    <EditIndustryModal
      v-model:visible="editIndustryModalVisible"
      :data="editIndustryModalData"
      :industry-list="industryList"
      :level="editIndustryModalLevel"
      :type="editIndustryModalType"
      @confirm="onEditIndustryConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { useData } from '../hooks/useData'
import type { IndustryOptions, PromptIndustry } from '../hooks/useData'
import EditIndustryModal from './EditIndustryModal.vue'

const {
  getPromptIndustryList,
  createPromptIndustry,
  updatePromptIndustry,
  updatePromptIndustrySort,
} = useData()

const industryOptions = ref<IndustryOptions[]>([])
const industryList = ref<PromptIndustry[]>([])
const industryTreeRef = ref<any>()

const editIndustryModalVisible = ref(false)
const editIndustryModalType = ref<'add' | 'edit'>('add')
const editIndustryModalLevel = ref(1)
const editIndustryModalData = ref<Partial<PromptIndustry>>({})

async function loadPromptIndustryList() {
  const { data, options } = await getPromptIndustryList()
  industryList.value = data
  industryOptions.value = options
}

function onCollapseAll() {
  // 遍历所有节点，设置为收起状态
  industryTreeRef.value?.store._getAllNodes().forEach((node: any) => {
    node.expanded = false
  })
}

function onExpandAll() {
  // 遍历所有节点，设置为展开状态
  industryTreeRef.value?.store._getAllNodes().forEach((node: any) => {
    node.expanded = true
  })
}

function onAddSiblingIndustry(data: IndustryOptions) {
  editIndustryModalType.value = 'add'
  editIndustryModalLevel.value = data.industry_level
  editIndustryModalData.value = {
    industry_level: data.industry_level,
    industry_parent: Number(data.industry_parent),
    sort: Number(data.sort),
  }
  editIndustryModalVisible.value = true
}

function onEditIndustry(data: IndustryOptions) {
  editIndustryModalType.value = 'edit'
  editIndustryModalLevel.value = data.industry_level
  editIndustryModalData.value = {
    industry_name: data.industry_name,
    industry_code: Number(data.industry_code),
    industry_parent: Number(data.industry_parent),
    enabled: Boolean(data.enabled),
    sort: Number(data.sort),
  }
  editIndustryModalVisible.value = true
}

function onEditIndustryConfirm(data: Partial<PromptIndustry>) {
  if (editIndustryModalType.value === 'add') {
    onCreateIndustryConfirm(data)
  }
  else {
    onUpdateIndustryConfirm(data)
  }
}

async function onCreateIndustryConfirm(data: Partial<PromptIndustry>) {
  const { error_code, error_msg } = await createPromptIndustry(data)
  if (error_code === 0) {
    ElMessage.success('新增行业成功')
    editIndustryModalVisible.value = false
    await loadPromptIndustryList()
  }
  else {
    ElMessage.error(`新增行业失败，原因：${error_msg}`)
  }
}

async function onUpdateIndustryConfirm(data: Partial<PromptIndustry>) {
  if (data.industry_parent === 0) {
    delete data.industry_parent
  }
  const { error_code, error_msg } = await updatePromptIndustry(data)
  if (error_code === 0) {
    ElMessage.success('更新行业成功')
    editIndustryModalVisible.value = false
    await loadPromptIndustryList()
  }
  else {
    ElMessage.error(`更新行业失败，原因：${error_msg}`)
  }
}

function allowDrop(draggingNode: any, dropNode: any, type: 'prev' | 'inner' | 'next') {
  // 一级节点只能在一级节点之间移动
  if (draggingNode.data.industry_level === 1) {
    // 只能放在其他一级节点的前后，不能放到内部
    return dropNode.data.industry_level === 1 && type !== 'inner'
  }
  // 二级节点只能在当前父级下移动
  if (draggingNode.data.industry_level === 2) {
    // 只能放在同一个父级下的二级节点前后，不能放到内部或其他父级
    return (
      dropNode.data.industry_level === 2
      && draggingNode.parent.data.industry_code === dropNode.parent.data.industry_code
      && type !== 'inner'
    )
  }
  // 其他情况不允许
  return false
}

async function onNodeDrop(draggingNode: any) {
  // 找到同级的节点数组
  let siblings: IndustryOptions[] = []
  if (draggingNode.data.industry_level === 1) {
    siblings = industryOptions.value
  }
  else if (draggingNode.data.industry_level === 2) {
    const dragNodeParentKey = industryOptions.value.find(item => item.industry_code === draggingNode.data.industry_parent)?.industry_code
    siblings = industryOptions.value.find(item => item.industry_code === dragNodeParentKey)?.children || []
  }

  const industryCodeList = siblings.map(item => Number(item.industry_code))

  const { error_code, error_msg } = await updatePromptIndustrySort(industryCodeList)
  if (error_code === 0) {
    ElMessage.success('更新行业排序成功')
  }
  else {
    ElMessage.error(`更新行业排序失败，原因：${error_msg}`)
  }

  // 记住当前所有节点的展开和收起的状态，重新加载完数据之后，恢复到原来的状态
  const expandedKeys = industryTreeRef.value?.store._getAllNodes().filter((node: any) => node.expanded).map((node: any) => node.data.industry_code)

  await loadPromptIndustryList()

  expandedKeys.forEach((key: number) => {
    const node = industryTreeRef.value?.getNode(key)
    if (node) {
      node.expanded = true
    }
  })
}

onMounted(async () => {
  // 加载行业
  loadPromptIndustryList()
})
</script>
