<template>
  <div class="px-8">
    <el-form
      ref="ruleFormRef"
      class="demo-ruleForm"
      label-width="auto"
      :model="ruleForm"
      :rules="rules"
      status-icon
      style="max-width: 600px"
    >
      <el-form-item label="用户编码" prop="user_code">
        <el-input v-model="ruleForm.user_code" disabled />
      </el-form-item>
      <el-form-item label="下载次数上限" prop="downloadCntLimit">
        <el-input
          v-model.number="ruleForm.downloadCntLimit"
          autocomplete="off"
          type="text"
        />
      </el-form-item>
      <el-form-item label="下载行数上限" prop="downloadRowLimit">
        <el-input
          v-model.number="ruleForm.downloadRowLimit"
          autocomplete="off"
          type="text"
        />
      </el-form-item>
    </el-form>
    <div class="flex gap-8">
      <div class="color-red font-bold mr-auto text-14">{{ statusMsg }}</div>
      <el-button @click="resetForm(ruleFormRef)">重置</el-button>
      <el-button type="primary" @click="submitForm(ruleFormRef)">更新</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAccountData } from '../hooks/useAccountData'
import type { AccountListItem } from '../hooks/types'

const props = defineProps<{
  account?: AccountListItem
}>()

const emits = defineEmits(['success'])

const { updateUserDownloadLimit, getUserDownloadLimit } = useAccountData()
interface RuleForm {
  user_code: string
  downloadCntLimit: number
  downloadRowLimit: number
}
const statusMsg = ref('')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  user_code: '',
  downloadCntLimit: 0,
  downloadRowLimit: 0,
})

const rules = reactive<FormRules<RuleForm>>({
  user_code: [
    { required: true, message: '用户编码必填', trigger: 'blur' },
  ],
  downloadCntLimit: [
    { required: true, message: '下载次数上限必填', trigger: 'blur' },
    { type: 'number', min: 0, message: '下载次数上限必须是数值', trigger: 'blur' },
  ],
  downloadRowLimit: [
    { required: true, message: '下载行数上限必填', trigger: 'blur' },
    { type: 'number', min: 0, message: '下载行数上限必须是数值', trigger: 'blur' },
  ],
})

watch(() => props.account, () => {
  initFormValue()
  statusMsg.value = ''
}, { immediate: true })

async function initFormValue() {
  if (!props.account) {
    ruleFormRef.value?.resetFields()
    statusMsg.value = ''
    return
  }
  ruleForm.user_code = props.account.user_code.toString()
  // 请求接口获取用户的下载次数和行数限制
  const { data } = await getUserDownloadLimit(ruleForm.user_code)
  ruleForm.downloadCntLimit = data?.download_cnt_limit || 30
  ruleForm.downloadRowLimit = data?.download_row_limit || 30
}

async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await updateUserDownloadLimit(
        ruleForm.user_code,
        {
          download_cnt_limit: ruleForm.downloadCntLimit,
          download_row_limit: ruleForm.downloadRowLimit,
        },
      )
      if (res.error_code) {
        statusMsg.value = res.error_msg
        return
      }
      statusMsg.value = ''
      ElMessage.success('更新成功')
      emits('success')
    }
    else {
      statusMsg.value = ''
      // console.log('error submit!', fields)
    }
  })
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  initFormValue()
  statusMsg.value = ''
}
</script>
