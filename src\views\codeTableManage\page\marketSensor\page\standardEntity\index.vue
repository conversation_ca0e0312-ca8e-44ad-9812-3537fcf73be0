<template>
  <div class="flex flex-col gap-16 h-full">
    <div class="flex justify-between">
      <el-segmented v-model="tab" :options="tabOptions" />
    </div>
    <StandardEntity v-if="tab === '实体管理'" />
    <StandardEntityType v-else-if="tab === '实体类型'" />
  </div>
</template>

<script setup lang="ts">
import StandardEntity from './components/StandardEntity.vue'
import StandardEntityType from './components/StandardEntityType.vue'

const tab = ref('实体管理')
const tabOptions = [
  { value: '实体管理', label: '实体管理' },
  { value: '实体类型', label: '实体类型' },
]
</script>
