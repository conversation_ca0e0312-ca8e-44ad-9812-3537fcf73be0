<template>
  <div class="px-8">
    <el-form
      ref="ruleFormRef"
      class="demo-ruleForm"
      label-width="auto"
      :model="ruleForm"
      :rules="rules"
      status-icon
      style="max-width: 600px"
    >
      <el-form-item label="邮箱" prop="account">
        <el-input v-model="ruleForm.account" />
      </el-form-item>
      <el-form-item label="用户名" prop="user_name">
        <el-input v-model="ruleForm.user_name" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="ruleForm.password" />
      </el-form-item>
      <el-form-item label="公司名称" prop="company_name">
        <el-input v-model="ruleForm.company_name" />
      </el-form-item>
      <el-form-item label="试用时间范围" prop="date">
        <el-date-picker
          v-model="ruleForm.date"
          clearable
          type="daterange"
        />
      </el-form-item>
      <el-form-item label="行业">
        <el-cascader
          v-model="ruleForm.industry_code_list"
          filterable
          :options="industryOptions"
          :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'mp_industry_code', checkStrictly: true }"
        />
      </el-form-item>
      <el-form-item label="账号类型">
        <el-select
          v-model="ruleForm.account_type"
          class="!w-190"
        >
          <el-option
            v-for="item in ACCOUNT_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="登录方式">
        <el-select
          v-model="ruleForm.login_type"
          class="!w-190"
        >
          <el-option
            v-for="item in LOGIN_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="flex gap-8">
      <div class="color-red font-bold mr-auto text-14">{{ statusMsg }}</div>
      <el-button @click="resetForm(ruleFormRef)">重置</el-button>
      <el-button type="primary" @click="submitForm(ruleFormRef)">创建</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { type FormInstance, type FormRules } from 'element-plus'
import { useAccountData } from '../hooks/useAccountData'
import { useAccountStore } from '@/stores/account'
import { ACCOUNT_TYPE_OPTIONS, LOGIN_TYPE_OPTIONS } from '@/constants/option'

const emits = defineEmits(['success'])
const { createAccount } = useAccountData()
const { industryOptions } = storeToRefs(useAccountStore())

interface RuleForm {
  account: string
  user_name: string
  company_name: string
  password: string
  date: string[]
  industry_code_list: string[]
  account_type: string
  login_type: number
}
const statusMsg = ref('')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  account: '',
  user_name: '',
  company_name: '',
  password: '',
  date: [],
  industry_code_list: ['0'],
  account_type: 'external',
  login_type: 0,
})

const rules = reactive<FormRules<RuleForm>>({
  account: [
    { required: true, message: '邮箱必填', trigger: 'blur' },
    { type: 'email', message: '邮箱格式错误', trigger: 'blur' },
  ],
  user_name: [
    { required: true, message: '用户名必填', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '密码必填', trigger: 'blur' },
  ],
})

async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await createAccount({
        account: ruleForm.account,
        username: ruleForm.user_name,
        password: ruleForm.password,
        company: ruleForm.company_name,
        effective_start_at: ruleForm.date?.[0] ? dayjs(ruleForm.date[0]).format('YYYY-MM-DD') : '',
        effective_end_at: ruleForm.date?.[1] ? dayjs(ruleForm.date[1]).format('YYYY-MM-DD') : '',
        industry_code: Number(ruleForm.industry_code_list[ruleForm.industry_code_list.length - 1]),
        industry_level: ruleForm.industry_code_list.length,
        account_type: ruleForm.account_type,
        login_type: ruleForm.login_type,
      })
      if (res.error_code) {
        statusMsg.value = res.error_msg
        return
      }
      statusMsg.value = ''
      ElMessage.success('创建成功')
      emits('success')
      ruleFormRef.value?.resetFields()
    }
    else {
      statusMsg.value = ''
      // console.log('error submit!', fields)
    }
  })
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  ruleForm.industry_code_list = ['0']
  ruleForm.account_type = 'external'
  ruleForm.login_type = 0
  statusMsg.value = ''
}
</script>
