<template>
  <div class="px-8">
    <el-form
      ref="ruleFormRef"
      class="demo-ruleForm"
      label-width="auto"
      :model="ruleForm"
      :rules="rules"
      status-icon
      style="max-width: 600px"
    >
      <el-form-item label="用户编码" prop="user_code">
        <el-input v-model="ruleForm.user_code" disabled />
      </el-form-item>
      <el-form-item label="试用时间范围" prop="date">
        <el-date-picker
          v-model="ruleForm.date"
          clearable
          type="daterange"
        />
      </el-form-item>
      <el-form-item label="账号状态">
        <el-radio-group v-model="ruleForm.is_enabled">
          <el-radio :value="true">正常</el-radio>
          <el-radio :value="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="行业">
        <el-cascader
          v-model="ruleForm.industry_code_list"
          filterable
          :options="industryOptions"
          :props="{ expandTrigger: 'hover', label: 'industry_name', value: 'mp_industry_code', checkStrictly: true }"
        />
      </el-form-item>
      <el-form-item label="账号类型">
        <el-select
          v-model="ruleForm.account_type"
          class="!w-190"
        >
          <el-option
            v-for="item in ACCOUNT_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="登录方式">
        <el-select
          v-model="ruleForm.login_type"
          class="!w-190"
        >
          <el-option
            v-for="item in LOGIN_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="flex gap-8">
      <div class="color-red font-bold mr-auto text-14">{{ statusMsg }}</div>
      <el-button @click="resetForm(ruleFormRef)">重置</el-button>
      <el-button type="primary" @click="submitForm(ruleFormRef)">更新</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAccountData } from '../hooks/useAccountData'
import type { AccountListItem } from '../hooks/types'
import { useAccountStore } from '@/stores/account'
import { findPathById } from '@/utils/data'
import { ACCOUNT_TYPE_OPTIONS, LOGIN_TYPE_OPTIONS } from '@/constants/option'

const props = defineProps<{
  account?: AccountListItem
}>()

const emits = defineEmits(['success'])

const { updateAccount } = useAccountData()
const { industryOptions } = storeToRefs(useAccountStore())
interface RuleForm {
  user_code: string
  is_enabled: boolean
  date: string[]
  industry_code_list: string[]
  account_type: string
  login_type: number
}
const statusMsg = ref('')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  user_code: '',
  is_enabled: true,
  date: [],
  industry_code_list: [],
  account_type: '',
  login_type: 0,
})

const rules = reactive<FormRules<RuleForm>>({
  user_code: [
    { required: true, message: '用户编码必填', trigger: 'blur' },
  ],
})

watch(() => props.account, () => {
  initFormValue()
  statusMsg.value = ''
}, { immediate: true })

function initFormValue() {
  if (!props.account) {
    ruleFormRef.value?.resetFields()
    statusMsg.value = ''
    return
  }
  ruleForm.user_code = props.account.user_code.toString()
  ruleForm.is_enabled = props.account.is_enabled
  ruleForm.industry_code_list = findPathById(industryOptions.value, 'mp_industry_code', props.account.industry_code.toString()) as string[] || []
  ruleForm.account_type = props.account.account_type
  ruleForm.login_type = props.account.login_type
  if (props.account.effective_start_at && props.account.effective_end_at) {
    ruleForm.date = [props.account.effective_start_at, props.account.effective_end_at]
  }
  else {
    ruleForm.date = []
  }
}

async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await updateAccount({
        user_code: ruleForm.user_code,
        is_enabled: ruleForm.is_enabled.toString(),
        effective_start_at: ruleForm.date?.[0] ? dayjs(ruleForm.date[0]).format('YYYY-MM-DD') : '',
        effective_end_at: ruleForm.date?.[1] ? dayjs(ruleForm.date[1]).format('YYYY-MM-DD') : '',
        account_type: ruleForm.account_type,
        industry_code: Number(ruleForm.industry_code_list[ruleForm.industry_code_list.length - 1]),
        industry_level: ruleForm.industry_code_list.length,
        login_type: ruleForm.login_type,
      })
      if (res.error_code) {
        statusMsg.value = res.error_msg
        return
      }
      statusMsg.value = ''
      ElMessage.success('更新成功')
      emits('success')
    }
    else {
      statusMsg.value = ''
      // console.log('error submit!', fields)
    }
  })
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  initFormValue()
  statusMsg.value = ''
}
</script>
