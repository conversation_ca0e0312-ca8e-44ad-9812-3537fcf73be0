<template>
  <div class="flex flex-col gap-16 h-full">
    <div class="flex justify-between">
      <el-segmented v-model="tab" :options="tabOptions" />
      <div class="flex gap-8 items-center">
        <span class="text-14">分拣类型</span>
        <el-segmented
          v-model="entityType"
          :options="entityTypeOptions"
          @change="init"
        />
      </div>
    </div>
    <el-steps
      :active="currentStep"
      finish-status="success"
    >
      <el-step v-for="step in steps" :key="step.step" :title="step.title">
        <template #title>
          <div class="flex items-center">
            <User v-if="step.type === 'human'" class="step-icon" />
            <Monitor v-if="step.type === 'computer'" class="step-icon" />
            <span>{{ step.title }}</span>
          </div>
        </template>
      </el-step>
    </el-steps>

    <!-- 静态组件列表，使用v-if控制显示 -->
    <div v-loading="dataStatus === 'loading'" class="flex-1">
      <UploadFile
        v-if="currentStep === 0"
        @upload-success="onUploadSuccess"
      />
      <JobRunStatus v-if="currentStep === 1" />
      <CheckStatusEntityList
        v-if="currentStep === 2"
        @save-success="getCurrentStep"
      />
      <JobRunStatus v-if="currentStep === 3" />
      <JobRunStatus
        v-if="currentStep === 4"
        @load="getCurrentStep"
      />
      <CompleteEntityList
        v-if="currentStep === 5"
        @save-success="getCurrentStep"
      />
      <AppAndWebMatchList
        v-if="currentStep === 6"
        @save-success="getCurrentStep"
      />
      <FinalCheckList
        v-if="currentStep === 7"
        @save-success="getCurrentStep"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Monitor, User } from '@element-plus/icons-vue'
import { useData } from './hooks/useData'
import { useFilter } from './hooks/useFilter'
import UploadFile from './components/UploadFile.vue'
import JobRunStatus from './components/JobRunStatus.vue'
import CheckStatusEntityList from './components/CheckStatusEntityList.vue'
import CompleteEntityList from './components/CompleteEntityList.vue'
import AppAndWebMatchList from './components/AppAndWebMatchList.vue'
import FinalCheckList from './components/FinalCheckList.vue'

const {
  getTaskList,
  getTaskStepList,
  getAllAiList,
  getAllAiIndustryList,
} = useData()
const {
  taskId,
  entityType,
  entityTypeOptions,
  allAiList,
  allAiIndustryList,
  currentStep,
} = useFilter()

// 功能模块
const tabOptions = [
  { value: 'current', label: '当前分拣流程' },
  { value: 'list', label: '历史分拣记录', disabled: true },
]
const tab = ref('current')

const steps = [
  { step: 1, title: '导入实体', type: 'human' },
  { step: 2, title: '标记实体状态', type: 'computer' },
  { step: 3, title: '复核实体状态', type: 'human' },
  { step: 4, title: '追加新增实体到总表', type: 'computer' },
  { step: 5, title: '量级校验', type: 'computer' },
  { step: 6, title: '字段补全', type: 'human' },
  { step: 7, title: 'APP/WEB关系匹配', type: 'human' },
  { step: 8, title: '最终收录审核', type: 'human' },
]

// 获取当前流程的步骤
async function getCurrentStep() {
  const { data: taskData } = await getTaskList({
    task_status: 'processing',
  })
  if (!taskData.length) {
    currentStep.value = 0
    taskId.value = 0
    return
  }

  taskId.value = taskData[0].task_id
  const { data: stepData } = await getTaskStepList(taskId.value)
  if (stepData.length) {
    currentStep.value = (Number(stepData.at(-1)?.step_type) - 1)
  }
}

const dataStatus = ref<DataStatus>('loading')
async function init() {
  dataStatus.value = 'loading'

  const promiseList = [
    getAllAiList(),
    getAllAiIndustryList(),
  ] as const
  const [{ data: aiList }, { data: aiIndustryList }] = await Promise.all(promiseList)
  allAiList.value = aiList || []
  allAiIndustryList.value = aiIndustryList || []

  await getCurrentStep()

  dataStatus.value = 'normal'
}
init()

// step1
async function onUploadSuccess() {
  currentStep.value = 2
}
</script>

<style lang="scss" scoped>
:deep(.el-step.is-horizontal .el-step__line) {
  left: 28px;
  right: 4px;
}
:deep(.el-step__title) {
  font-size: 14px;
  line-height: 28px;
}
.step-icon {
  @apply w-16;
}
</style>
