<template>
  <div class="flex flex-col gap-16 h-full w-full">
    <div class="flex items-center justify-between">
      <SmIndustrySelector
        v-model="industryCodeList"
        @change="onIndustryChange"
      />
      <div class="flex gap-8">
        <el-button @click="openUploadDialog">导入</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </div>
    <Handsontable
      ref="hotTableRef"
      class="flex-1"
      :columns="columns"
      :data="tableData"
      :fixed-columns-left="3"
      :post-transform="postTransform"
      :transform="transform"
      @confirm-changes="onConfirmChanges"
    />
    <!-- 导入弹窗 -->
    <UploadDialog
      v-model="uploadDialogVisible"
      :template-filename="templateFilename"
      :template-url="templateUrl"
      @upload="handleUpload"
    />
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { SmPdCodeTable } from './hook/useData'
import { useData } from './hook/useData'
import SmIndustrySelector from '../../components/SmIndustrySelector.vue'
import UploadDialog from '../../components/UploadDialog.vue'

const dimensionOptions = [
  { label: '应用场景', value: 'use_cases' },
  { label: '用户反馈（正面）', value: 'user_feedback_pos' },
  { label: '用户反馈（负面）', value: 'user_feedback_neg' },
  { label: '购买动机', value: 'buying_motives' },
  { label: '用户期望', value: 'user_expectations' },
]

const {
  getCodeTableList,
  updateCodeTable,
  addByXlsx,
} = useData()

const { smIndustryOptions } = storeToRefs(useAccountStore())
const industryCodeList = ref<string[]>([])
const hotTableRef = shallowRef()
const uploadDialogVisible = ref(false)
const templateUrl = 'https://osscdn.datastory.com.cn/creative/static/resource/73434152c138a8b61ceaccb50cbb25c6.xlsx'
const templateFilename = '产品讨论码表批量上传模板.xlsx'

function initFilter() {
  industryCodeList.value = [smIndustryOptions.value[0].industry_code]
}
initFilter()

function onIndustryChange() {
  loadData()
}

function transform(list: SmPdCodeTable[]) {
  return list.map(data => ({
    ...data,
    analysis_scenarios: dimensionOptions.find(_ => _.value === data.analysis_scenarios)?.label,
  }))
}

function postTransform(list: SmPdCodeTable[]) {
  return list.map(data => ({
    ...data,
    analysis_scenarios: dimensionOptions.find(_ => _.label === data.analysis_scenarios)?.value,
  }))
}

const columns = [
  { name: 'industry_name', label: '行业名称' },
  {
    name: 'analysis_scenarios',
    label: '分析场景',
    editor: 'select',
    selectOptions: dimensionOptions.map(_ => _.label),
  },
  { name: 'dimension', label: '维度' },
  { name: 'keywords', label: '关键词' },
  { name: 'filter_words', label: '过滤词' },
  { name: 'version', label: '版本' },
]

const tableData = ref<SmPdCodeTable[]>([])
async function loadData() {
  const res = await getCodeTableList(industryCodeList.value.at(-1) || '')
  tableData.value = res.data
}
loadData()

function save() {
  hotTableRef.value?.showConfirmDialog()
}

async function onConfirmChanges(changes: any[]) {
  const { error_code, error_msg, data } = await updateCodeTable(changes)
  if (error_code === 0) {
    ElMessage.success(`保存成功,更新了${data.rows}条记录`)
  }
  else {
    ElMessage.error(error_msg)
  }
  loadData()
}

// 打开上传弹窗
function openUploadDialog() {
  uploadDialogVisible.value = true
}

// 处理上传文件
async function handleUpload(file: File) {
  try {
    const res = await addByXlsx(file)
    if (res.error_code === 0) {
      ElMessage.success('添加成功')
      uploadDialogVisible.value = false
      loadData()
    }
    else {
      ElMessage.error(res.error_msg)
    }
  }
  catch (error) {
    ElMessage.error(`上传失败：${error}`)
  }
}
</script>

<style scoped>

</style>
