<template>
  <div class="row">
    <div class="label">
      {{ NUMBER_CHINESE[index] }}{{ label }}
    </div>
    <div class="value-wrapper">
      <div
        v-for="item in categoryList"
        :key="item.mp_industry_code"
        class="value"
        :class="{
          active: modelValue.length === index && modelValue[index - 1] === item.mp_industry_code,
        }"
        @click="onCategoryChange([...modelValue.slice(0, index - 1), item.mp_industry_code])"
      >
        {{ item.industry_name }}
      </div>
    </div>
  </div>
  <CategoryList
    v-if="selectedCategoryChildren.length"
    :category-list="selectedCategoryChildren"
    :index="index + 1"
    :label="label"
    :model-value="modelValue"
    @update:model-value="onCategoryChange($event)"
  />
</template>

<script lang="ts">
import { NUMBER_CHINESE } from '@/constants/option'

export default { name: 'CategoryList' }
</script>

<script setup lang="ts">
const props = defineProps<{
  index: number
  label: string
  categoryList: any[]
  modelValue: string[]
}>()
const emit = defineEmits(['update:modelValue', 'change'])
const selectedCategoryChildren = computed(() => {
  return props.categoryList.find(
    item => item.mp_industry_code === props.modelValue[props.index - 1],
  )?.children || []
})

function onCategoryChange(codeList: string[]) {
  emit('update:modelValue', codeList)
  emit('change')
}
</script>

<style lang="scss" scoped>
.row {
  @apply text-14 text-text;
  display: flex;
  gap: 16px;

  .label {
    @apply w-84 pt-11 shrink-0 fw-bold;
  }

  .value-wrapper {
    display: flex;
    flex-wrap: wrap;
    column-gap: 8px;

    .value {
      @apply p-11 cursor-pointer flex items-center;
      border-radius: 36px;
      caret-color: transparent;

      &.active {
        @apply bg-primary text-white;
      }

      &.disabled {
        @apply text-gray pointer-events-none;
      }
      &.lock {
        @apply pointer-events-none;
      }
      // &.lock::before {
      //   @apply text-text/50 ;
      //   content: '\e66a';
      //   font-family: 'iconfont';
      //   font-size: 14px;
      // }
    }
  }
}
</style>
