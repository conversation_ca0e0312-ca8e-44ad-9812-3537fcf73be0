import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { router } from '@/routers'

// import { ElNotification } from 'element-plus'

import { getToken, removeToken } from '@/utils/cookies'
import { merge } from 'lodash-es'

function createInstance() {
  const instance: AxiosInstance = axios.create()

  instance.interceptors.request.use(
    config => config,
    error => Promise.reject(error),
  )

  instance.interceptors.response.use(
    (response) => {
      // console.log('interceptors response', response)
      const apiData = response.data

      const responseType = response.request?.responseType
      if (responseType === 'blob' || responseType === 'arraybuffer') {
        return apiData
      }
      const code = apiData.code
      // auth 相关接口有code 其他没有
      if (code === undefined) {
        return apiData
      }
      switch (code) {
        case 0:
          return apiData
        case 20000:
          return apiData
        case 50005:
          // Token 过期时
          removeToken()
          router.replace({ path: '/login' })
          return Promise.reject(new Error(apiData.message || 'Error'))
        default:
          return apiData
      }
    },
    (error) => {
      const httpCode = error.response.status
      const responseData = error.response.data
      switch (httpCode) {
        case 401:
          removeToken()
          router.replace({ path: '/login' })
          return Promise.reject(new Error(responseData.message || 'Error'))
        default:
          return Promise.reject(error)
      }
    },
  )

  return instance
}

function createRequest(instance: AxiosInstance) {
  return function<T>(config: AxiosRequestConfig): Promise<T> {
    const token = getToken()
    const defaultConfig = {
      headers: {
        'Authorization': token ? `Bearer ${token}` : undefined,
        'Content-Type': 'application/json',
      },
      timeout: 600000,
      // baseURL: import.meta.env.BASE_URL,
      baseURL: '/',
      data: {},
    }
    const mergeConfig = merge(defaultConfig, config)
    return instance(mergeConfig)
  }
}

const instance = createInstance()

export const request = createRequest(instance)
