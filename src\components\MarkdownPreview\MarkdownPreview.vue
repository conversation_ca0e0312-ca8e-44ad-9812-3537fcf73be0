<template>
  <VMdPreview :text="text" />
</template>

<script setup lang="ts">
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import createCopyCodePlugin from '@kangc/v-md-editor/lib/plugins/copy-code/index'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import hljs from 'highlight.js'
import '@kangc/v-md-editor/lib/plugins/copy-code/copy-code.css'
import '@kangc/v-md-editor/lib/style/preview.css'
import './github-theme.css'
import './github-code-light.scss'
import './github-code-dark.scss'
import './preview.scss'

defineProps<{
  text: string
}>()

VMdPreview.use(githubTheme, {
  Hljs: hljs,
})
VMdPreview.use(createCopyCodePlugin())
</script>
