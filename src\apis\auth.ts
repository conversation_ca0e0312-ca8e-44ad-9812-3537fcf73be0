import { request } from '@/utils/request'
import type { AccountPermissionConfig } from '@/views/account/hooks/types'

export interface LoginRequestData {
  username: string
  password: string
  code?: string
}

export interface LoginUserInfo {
  token: string
  account: string
  user_code: string
  user_name: string
}

export interface LoginResponse {
  code: number
  data: LoginUserInfo
  message: string
}

export interface UserInfoResponse {
  code: number
  data: {
    user_code: string
    user_name: string
    account: string
    permissions: AccountPermissionConfig
  }
  message: string
}

export function login(data: LoginRequestData): Promise<LoginResponse> {
  return request({
    url: 'ops/v2/auth/login',
    method: 'post',
    data: {
      ...data,
    },
    headers: {
      Authorization: '', // 必传
    },
  })
}

export function logout(): Promise<unknown> {
  return request({
    url: 'ops/v2/auth/logout',
    method: 'post',
  })
}

export function userInfo(): Promise<UserInfoResponse> {
  return request({
    url: 'ops/v2/auth/info',
    method: 'post',
  })
}
