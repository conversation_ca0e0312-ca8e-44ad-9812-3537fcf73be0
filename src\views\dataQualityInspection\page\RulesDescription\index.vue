<template>
  <div>
    <h1>规则列表</h1>
    <el-table :data="tableData">
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column label="规则名称" prop="ruleName" />
      <el-table-column label="规则说明" prop="ruleDesc" />
      <el-table-column :formatter="coverRangeFormatter" label="已覆盖范围" prop="coverRange" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
const tableData = [
  {
    ruleName: '值为负值',
    ruleDesc: '值不应该为负值的，在产品中显示为负值。例如：活跃用户数，出现负值。',
    coverRange: ['AI 全息', 'APP 全息'],
  },
  {
    ruleName: '数据重复',
    ruleDesc: '产品中的同一个列表，实体名称相同的有多条，即为数据重复。例如，表格中出现2条豆包的数据，即豆包出现了数据重复。',
    coverRange: ['AI 全息', 'APP 全息'],
  },
  // {
  //   ruleName: '连续0值',
  //   ruleDesc: '连续2个周期及以上为0值',
  //   coverRange: ['AI 全息', 'APP 全息']
  // },
  // {
  //   ruleName: '连续空值',
  //   ruleDesc: '连续2个周期及以上为空值',
  //   coverRange: ['AI 全息', 'APP 全息']
  // },
  // {
  //   ruleName: '突然为0',
  //   ruleDesc: '突然某个周期为0值',
  //   coverRange: ['AI 全息', 'APP 全息']
  // },
  // {
  //   ruleName: '用户画像相加不等于1',
  //   ruleDesc: '用户画像值相加不等于1，例如：男性+女性比例不等于1',
  //   coverRange: ['AI 全息', 'APP 全息']
  // }
]
function coverRangeFormatter(row: any) {
  return row.coverRange.join('，')
}
</script>

<style scoped>

</style>
