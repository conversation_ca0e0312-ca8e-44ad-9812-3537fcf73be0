export interface StandardEntityCodeTable {
  entity_code: number
  entity_name: string
  entity_type_code: number
  filter_words?: string
  keywords: string
  created_at: string
  created_by?: number
  data_domain?: string
  updated_at: string
  updated_by?: number
}

export interface EntityTypeTable {
  created_at: string
  created_by: string
  entity_type_cn: string
  entity_type_code: number
  entity_type_en: string
  id: number
  sort: number
  updated_at: string
  updated_by: number
}

export function useData() {
  // 查询标准实体列表
  async function getStandardEntityList(searchValue: string) {
    const params = {
      methodName: 'standard_entity_list',
      entity_name: searchValue,
    }
    const res = await talon<PERSON>pi<StandardEntityCodeTable[]>(params)
    return res
  }

  // 编辑实体码表
  async function editStandardEntity(data: {
    entity_code: number
    entity_name: string
    entity_type_code: number
    filter_words: string
    keywords: string
    data_domain: string
  }) {
    const res = await talonApi({
      methodName: 'standard_entity_update',
      ...data,
    })
    return res
  }

  // 删除实体码表
  async function deleteStandardEntity(entityCode: number) {
    const res = await talonApi({
      methodName: 'standard_entity_delete',
      entity_code: entityCode,
    })
    return res
  }

  // 新增实体码表
  async function uploadStandardEntity(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'standard_entity_batch_create')
    formData.append('file', file)
    const res = await talonApi(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  async function getEntityTypeList(entity_type_cn?: string) {
    const res = await talonApi<EntityTypeTable[]>({
      methodName: 'standard_entity_type_list',
      entity_type_name: entity_type_cn,
    })
    return res
  }

  async function addOrEditEntityType(data: {
    id?: number
    entity_type_cn: string
    entity_type_en: string
    entity_type_code?: number
    sort?: number
  }) {
    const res = await talonApi({
      methodName: 'standard_entity_type_update',
      ...data,
    })
    return res
  }

  async function deleteEntityType(id: number) {
    const res = await talonApi({
      methodName: 'standard_entity_type_delete',
      id,
    })
    return res
  }

  async function updateEntityCache() {
    const res = await actionApiV2({
      methodName: 'update_standard_entity_codetable',
      params: {},
    })
    return res
  }

  return {
    getStandardEntityList,
    editStandardEntity,
    deleteStandardEntity,
    uploadStandardEntity,
    getEntityTypeList,
    addOrEditEntityType,
    deleteEntityType,
    updateEntityCache,
  }
}
