import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import enUS from './en-US'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn.js'

export type Language = 'zh-CN' | 'en-US'
type MessageSchema = typeof zhCN
export const i18n = createI18n<[MessageSchema], Language, false>({
  legacy: false,
  locale: localStorage.getItem('language') ?? 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
  },
  silentFallbackWarn: true,
  missingWarn: false,
  fallbackWarn: false,
})

export async function setLanguage(lang: 'zh-CN' | 'en-US') {
  i18n.global.locale.value = lang
  switch (lang) {
    case 'zh-CN':
      dayjs.locale('zh-cn')
      break
    case 'en-US':
      dayjs.locale('en')
      break
  }
  localStorage.setItem('language', lang)
}
