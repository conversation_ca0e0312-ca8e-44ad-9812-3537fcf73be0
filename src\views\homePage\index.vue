<template>
  <div>
    <!-- 行业 -->
    <div class="h-150 max-w-888 overflow-auto">
      <CategoryList
        v-model="selectedCodeList"
        :category-list="industryOptions"
        :index="1"
        label="级行业"
        @change="onCategoryChange"
      />
    </div>
    <!-- 选项卡 -->
    <div class="custom-style mt-12">
      <el-segmented v-model="tab" :options="tabOptions" size="large" />
    </div>
    <!-- 图片表单 -->
    <template v-if="tab === '首页配图'">
      <el-alert class="!mt-16" :closable="false" title="内链就是产品链接#后的路径，比如 xsignal-ai.com/#/ai_product 中的 /ai_product" type="warning" />
      <el-form
        label-width="auto"
        :model="imageForm"
      >
        <h3>第一屏</h3>
        <div class="w-fit">
          <div class="flex flex-col gap-24 items-center">
            <el-image class="!flex-center b-rd-24 flex-shrink-0 min-h-270 shadow w-480" fit="contain" :src="imageForm.screen1Image.url">
              <template #error>
                <div class="flex-center h-full text-24 text-gray w-full">图片为空 / url 加载失败</div>
              </template>
            </el-image>
            <div class="flex flex-col gap-8 w-full">
              <div class="flex gap-8 items-center">
                <span class="flex-shrink-0">地址</span>
                <el-input v-model="imageForm.screen1Image.url" />
              </div>
              <div class="flex gap-8 items-center">
                <span class="flex-shrink-0">内链</span>
                <el-input v-model="imageForm.screen1Image.href" />
              </div>
            </div>
          </div>
        </div>
        <h3>轮播图</h3>
        <div class="flex gap-24 overflow-auto py-8">
          <div
            v-for="(item, index) in imageForm.swiperImageList"
            :key="index"
            class="flex-shrink-0 relative"
          >
            <div class="flex flex-col gap-24 items-center">
              <el-image class="!flex-center  b-rd-24  flex-shrink-0  min-h-270  shadow  w-480" fit="contain" :src="item.url">
                <template #error>
                  <div class="flex-center h-full text-24 text-gray w-full">图片为空 / url 加载失败</div>
                </template>
              </el-image>
              <div class="flex flex-col gap-8 w-full">
                <div class="flex gap-8 items-center">
                  <span class="flex-shrink-0">地址</span>
                  <el-input v-model="item.url" />
                </div>
                <div class="flex gap-8 items-center">
                  <span class="flex-shrink-0">内链</span>
                  <el-input v-model="item.href" />
                </div>
              </div>
            </div>
            <div
              class="absolute b-1 b-primary b-rd-50% b-solid bg-bg-white cursor-pointer dark:bg-bg-black flex-center h-24 right--4 text-primary top--4 w-24"
              @click="removeImage(index)"
            >
              <IEpClose />
            </div>
          </div>
          <div class="b-rd-24 cursor-pointer flex-shrink-0 h-270 shadow w-480" @click="addImage">
            <div class="flex-center  h-full  text-48  text-gray  w-full">+</div>
          </div>
        </div>
      </el-form>
      <el-divider />
      <div class="text-end">
        <el-button type="primary" @click="submitImage">保存</el-button>
      </div>
    </template>
    <!-- AI默认问题表单 -->
    <template v-else-if="tab === 'Rosetta X 推荐问题'">
      <el-segmented v-model="aiType" class="my-24" :options="aiTypeOptions" @change="onSegmentedChange" />
      <el-form
        class="flex flex-col gap-24 max-w-888"
        label-width="auto"
        :model="questionForm"
      >
        <el-form-item
          v-for="(item, index) in questionForm.questionList"
          :key="index"
          :label="`问题${index + 1}`"
          required
        >
          <div class="flex gap-12 w-full">
            <el-input v-model="item.text" />
            <span class="cursor-pointer" @click="questionForm.questionList.splice(index, 1)">X</span>
          </div>
        </el-form-item>
        <el-button class="w-full" @click="questionForm.questionList.push({ text: '' })">+</el-button>
      </el-form>
      <el-divider />
      <div class="text-end">
        <el-button type="primary" @click="submitQuestionList">保存</el-button>
      </div>
    </template>
    <!-- AI推荐问题表单 -->
    <template v-else>
      <Event
        :selected-code-list="selectedCodeList"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { useAccountStore } from '@/stores/account'
import CategoryList from './components/CategoryList.vue'
import Event from './page/Event/Event.vue'
import type { ImageInfo } from './hooks/useData'
import { useData } from './hooks/useData'
import { ElMessage } from 'element-plus'

const { industryOptions } = storeToRefs(useAccountStore())
const { getImageList, setImageList, getAiQuestionList, setAiQuestionList } = useData()

const tab = ref('首页配图')
const tabOptions = ['首页配图', 'Rosetta X 推荐问题', '首页事件', '图片生成']

// 类目
const selectedCodeList = ref<string[]>(['0'])
const queryCode = computed(() => Number(selectedCodeList.value[selectedCodeList.value.length - 1]))
function onCategoryChange() {
  loadImageList()
  loadAiQuestionList()
}

// 图表单
interface ImageForm {
  screen1Image: Pick<ImageInfo, 'href' | 'url'>
  swiperImageList: Pick<ImageInfo, 'href' | 'url'>[]
}
const imageForm = ref<ImageForm>({
  screen1Image: { href: '', url: '' },
  swiperImageList: [],
})
function addImage() {
  imageForm.value.swiperImageList.push({ href: '', url: '' })
}
function removeImage(index: number) {
  imageForm.value.swiperImageList.splice(index, 1)
}

// 默认问题表单
interface QuestionForm {
  questionList: { text: string }[]
}
const aiType = ref('realtime_detect')
const aiTypeOptions = [
  {
    label: 'Real-time AI 探测',
    value: 'realtime_detect',
  }, {
    label: 'DataGPT',
    value: 'datagpt',
    disabled: true,
  }, {
    label: 'AI Report',
    value: 'ai_report',
  },
]
const questionForm = ref<QuestionForm>({
  questionList: [],
})

// 图
const imageList = ref<ImageInfo[]>([])
async function loadImageList() {
  const { data } = await getImageList(queryCode.value)
  imageList.value = data
  imageForm.value.screen1Image = data.find(item => item.sort === 1) || { href: '', url: '' }
  imageForm.value.swiperImageList = data.filter(item => item.sort !== 1)
}
async function submitImage() {
  if (imageForm.value.screen1Image.url === '' || imageForm.value.screen1Image.href === '') {
    ElMessage.error('第一屏图片地址和内链不能为空')
    return
  }
  if (imageForm.value.swiperImageList.some(item => item.url === '')) {
    ElMessage.error('轮播图图片地址不能为空')
    return
  }
  const { error_code, error_msg } = await setImageList({
    industry_code: queryCode.value,
    industry_level: selectedCodeList.value.length,
    url_list: [
      imageForm.value.screen1Image,
      ...imageForm.value.swiperImageList,
    ],
  })
  if (error_code === 0) {
    ElMessage.success('保存成功')
    loadImageList()
  }
  else {
    ElMessage.error(error_msg)
  }
}

// ai 问题
async function loadAiQuestionList() {
  const res = await getAiQuestionList(queryCode.value)
  if (!res) return
  questionForm.value.questionList = res.data.find(_ => _.ai_type === aiType.value)?.question_list?.map(_ => ({ text: _ })) || []
}
async function submitQuestionList() {
  if (questionForm.value.questionList.some(_ => _.text === '')) {
    ElMessage.error('问题不能为空')
    return
  }
  const question_list = questionForm.value.questionList.map(_ => _.text)
  const ai_type = aiType.value

  const { error_code, error_msg } = await setAiQuestionList({
    industry_code: queryCode.value,
    industry_level: selectedCodeList.value.length,
    ai_type,
    question_list,
  })

  if (error_code === 0) {
    ElMessage.success('保存成功')
    loadAiQuestionList()
  }
  else {
    ElMessage.error(error_msg)
  }
}
function onSegmentedChange() {
  loadAiQuestionList()
}

loadImageList()
loadAiQuestionList()
</script>

<style lang="scss" scoped>
.custom-style .el-segmented {
  --el-border-radius-base: 16px;
}
</style>
