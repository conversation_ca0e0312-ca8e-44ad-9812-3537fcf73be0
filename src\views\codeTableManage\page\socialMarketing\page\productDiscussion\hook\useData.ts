export interface SmPdCodeTable {
  id: number
  industry_level: number
  industry_code: number
  industry_name: string
  industry_parent: number
  analysis_scenarios: string
  dimension: string
  keywords: string
  filter_words: string
  version: number
}
export function useData() {
  async function getCodeTableList(industry_code: string) {
    const res = await talon<PERSON>pi<SmPdCodeTable[]>({
      methodName: 'sm_pd_codetable_list',
      industry_code: Number(industry_code),
    })
    return res
  }

  async function updateCodeTable(data: SmPdCodeTable[]) {
    const res = await talonApi<{ rows: number }>({
      methodName: 'sm_pd_codetable_update',
      data,
    })
    return res
  }

  async function addByXlsx(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'sm_pd_codetable_batch_create')
    formData.append('file', file)
    const res = await talonApi(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  return {
    getCodeTableList,
    updateCodeTable,
    addByXlsx,
  }
}
