<template>
  <el-dialog
    destroy-on-close
    :model-value="visible"
    :title="title"
    width="600px"
    @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="p-16">
      <el-form label-position="left" label-width="110px" :model="form">
        <el-form-item v-if="!isNew" label="实体类型编码">
          <div>{{ form.entity_type_code }}</div>
        </el-form-item>
        <el-form-item label="实体类型">
          <el-input v-model="form.entity_type_cn" />
        </el-form-item>
        <el-form-item label="实体类型 key">
          <el-input v-model="form.entity_type_en" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input v-model="form.sort" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex gap-16 justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { EntityTypeTable } from '../hooks/useData'

const props = defineProps<{
  visible: boolean
  entityData: EntityTypeTable
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', formData: Partial<EntityTypeTable>): void
}>()

const isNew = computed(() => !props.entityData.id)
const title = computed(() => isNew ? '新增实体类型' : '编辑实体类型')

const form = ref<Partial<EntityTypeTable>>({
  id: 0,
  entity_type_cn: '',
  entity_type_code: 0,
  entity_type_en: '',
  sort: 0,
})

// 监听 activityData 变化，更新表单数据
watch(() => props.visible, () => {
  if (props.visible) {
    form.value = cloneDeep({
      ...props.entityData,
    })
  }
}, { immediate: true, deep: true })

function handleClose() {
  emit('update:visible', false)
}

function handleConfirm() {
  emit('confirm', cloneDeep({
    ...form.value,
  }))
}
</script>

<style scoped>
.p-16 {
  padding: 16px;
}
.border-b {
  border-bottom: 1px solid #ebeef5;
}
</style>
