<template>
  <div class="">
    <el-upload
      v-loading="uploadLoading"
      accept=".xlsx"
      :disabled="uploadLoading"
      drag
      element-loading-text="上传中"
      :http-request="onBatchUpload"
      :show-file-list="false"
    >
      <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
      <div class="el-upload__text">
        将文件拖到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="flex items-center justify-between mt-4">
          <el-link :icon="Download" type="primary" :underline="false" @click="downloadTemplate">下载模板</el-link>
          <div class="el-upload__tip">
            文件仅支持.xlsx格式，一次仅支持上传1个文件
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, type UploadRequestOptions } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { useData } from '../hooks/useData'
import { useFilter } from '../hooks/useFilter'

const emit = defineEmits<{
  (e: 'uploadSuccess'): void
}>()

const {
  uploadAiWebEntityFile,
} = useData()
const {
  entityType,
  taskId,
} = useFilter()

const templateInfo = computed(() => {
  if (entityType.value === 'ai_web') {
    return {
      templateName: 'AI WEB分拣上传模板.xlsx',
      templateUrl: 'https://osscdn.datastory.com.cn/creative/static/resource/e29a1f5b9c6006ce65b38e71873d4a57.xlsx',
    }
  }
  else {
    return {
      templateName: 'AI APP分拣上传模板.xlsx',
      templateUrl: 'https://osscdn.datastory.com.cn/creative/static/resource/454d599816b267b111f52d5c45c0480c.xlsx',
    }
  }
})

const uploadLoading = ref(false)
async function onBatchUpload(options: UploadRequestOptions) {
  try {
    // 判断文件格式是否为.xlsx
    const fileName = options.file.name
    const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase()
    if (fileExt !== '.xlsx') {
      ElMessage.error('文件格式错误，请上传.xlsx格式的文件')
      return
    }

    uploadLoading.value = true
    const { data, error_code, error_msg } = await uploadAiWebEntityFile(options.file)
    if (error_code !== 0) {
      ElMessage.error(error_msg)
      return
    }
    taskId.value = data.task_id
    uploadLoading.value = false
    emit('uploadSuccess')
    ElMessage.success('导入成功')
  }
  catch (e) {
    ElMessage.error('上传失败')
    uploadLoading.value = false
  }
}

// 下载模板
async function downloadTemplate() {
  const url = templateInfo.value.templateUrl
  const filename = templateInfo.value.templateName

  try {
    const response = await fetch(url)
    const blob = await response.blob()

    const objectUrl = window.URL.createObjectURL(new Blob([blob], {
      type: 'application/octet-stream',
    }))

    const link = document.createElement('a')
    link.href = objectUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(objectUrl)
  }
  catch (error) {
    console.error('下载模板失败:', error)
  }
}
</script>
