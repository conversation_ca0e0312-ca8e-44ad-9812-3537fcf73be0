// 获取树形结构中某一项的路径
export function findPathById<T extends { children?: T[] }>(
  tree: T[],
  keyName: keyof T,
  id: T[typeof keyName],
  path: Array<T[keyof T]> = [],
): Array<Omit<T, 'children'>[keyof Omit<T, 'children'>]> | null {
  for (const node of tree) {
    // 更新路径，加入当前节点的 ID
    const currentPath = [...path, node[keyName]]

    // 如果找到目标节点，返回当前路径
    if (node[keyName] === id) {
      return currentPath as Array<Omit<T, 'children'>[keyof Omit<T, 'children'>]>
    }

    // 如果当前节点有子节点，递归搜索
    if (node.children) {
      const result = findPathById(node.children, keyName, id, currentPath)
      if (result) {
        return result
      }
    }
  }

  // 如果没有找到，返回 null
  return null
}

// 获取树形结构中的某一项
export function findNodeById<T extends { children?: T[] }>(
  tree: T[],
  keyName: keyof T,
  id: T[typeof keyName],
): T | null {
  for (const node of tree) {
    if (node[keyName] === id) {
      return node
    }
    if (node.children) {
      const result = findNodeById(node.children, keyName, id)
      if (result) {
        return result
      }
    }
  }
  return null
}

/**
 * 根据提供的选项格式化数字。
 * @param value - 要格式化的数字。
 * @param optionsOrUnit - 格式化选项或单位值。
 * @param decimalParam - 要显示的小数位数。
 * @returns 格式化后的数字字符串。
 */
interface FormatNumberOptions {
  unit?: number
  sign?: boolean
  decimal?: number
  axis?: boolean
}
type AvailableNumber = number | null | undefined
export function formatNumber(value: AvailableNumber, options: FormatNumberOptions): string
export function formatNumber(value: AvailableNumber, unit?: number, decimal?: number): string
export function formatNumber(value: AvailableNumber, optionsOrUnit?: FormatNumberOptions | number, decimalParam?: number) {
  if (value === undefined || value === null || isNaN(Number(value))) return '-'

  let unit = 1
  let sign = false
  let decimal = 0
  let axis = false
  if (typeof optionsOrUnit === 'object') {
    unit = optionsOrUnit.unit ?? unit
    sign = optionsOrUnit.sign ?? sign
    decimal = optionsOrUnit.decimal ?? decimal
    axis = optionsOrUnit.axis ?? axis
  }
  else {
    unit = optionsOrUnit ?? unit
    decimal = decimalParam ?? decimal
  }

  // 除以单位
  const unitValue = value / unit

  // 小值处理
  if (!axis) {
    const minimumValue = (10 ** -decimal)
    if (unitValue < minimumValue && unitValue > 0) return sign ? `<+${minimumValue}` : `<${minimumValue}`
  }
  // 轴线处理
  else {
    return Number(unitValue.toFixed(2)).toLocaleString()
  }

  // 千分位格式化
  const numberFormatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimal,
    maximumFractionDigits: decimal,
  })
  let formattedValue = numberFormatter.format(unitValue)

  // 符号位
  if (sign && unitValue > 0) formattedValue = `+${formattedValue}`

  return formattedValue
}

/**
 * 根据提供的选项格式化成百分比。
 * @param value - 要格式化的数字。
 * @param decimalOrOptions - 格式化选项或显示的小数位数。
 * @param signParams - 是否显示符号位。
 * @returns 格式化后的数字字符串。
 */
type FormatPercentOptions = Pick<FormatNumberOptions, 'decimal' | 'sign' | 'axis'>
export function formatPercent(value: AvailableNumber, options: FormatPercentOptions): string
export function formatPercent(value: AvailableNumber, decimal?: number, sign?: boolean): string
export function formatPercent(value: AvailableNumber, decimalOrOptions?: number | FormatPercentOptions, signParams = false) {
  if (value === undefined || value === null || isNaN(Number(value))) return '-'

  let sign = false
  let decimal = 2
  let axis = false
  if (typeof decimalOrOptions === 'object') {
    decimal = decimalOrOptions.decimal ?? decimal
    sign = decimalOrOptions.sign ?? sign
    axis = decimalOrOptions.axis ?? axis
  }
  else {
    decimal = decimalOrOptions ?? decimal
    sign = signParams
  }

  // 小值处理
  if (!axis) {
    const minimumValue = (10 ** -decimal) / 100
    if (value < minimumValue && value > 0) return sign ? `<+${minimumValue * 100}%` : `<${minimumValue * 100}%`
    if (value > -minimumValue && value < 0) return '<0%'
  }
  // 轴线处理
  else {
    return `${Number((value * 100).toFixed(2)).toLocaleString()}%`
  }

  // 百分号处理
  const numberFormatter = new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimal,
    maximumFractionDigits: decimal,
  })
  let formattedValue = numberFormatter.format(value)

  // 符号位
  if (sign && value > 0) formattedValue = `+${formattedValue}`

  return formattedValue
}
