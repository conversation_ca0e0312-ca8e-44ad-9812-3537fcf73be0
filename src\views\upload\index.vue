<template>
  <div class="mx-auto w-888">
    <el-upload
      accept=".jpg,.jpeg,.png,.webp"
      :disabled="loading"
      drag
      :http-request="upload"
      multiple
      :show-file-list="false"
    >
      <div v-loading="loading" class="flex-center flex-col h-200">
        <div class="mt-2 text-gray-500">
          拖拽文件 或 <em class="cursor-pointer font-medium hover:text-primary text-primary">点击上传</em>
        </div>
      </div>
      <template #tip>
        <div class="mt-8 text-gray-500">
          {{ '上传后需要等待 < 5分钟,成功后大产研通知群会发通知。' }}
        </div>
        <div class="text-gray-500">
          支持jpg、jpeg、png、webp格式
        </div>
      </template>
    </el-upload>
    <div class="h-24 w-24" />
    <div v-show="responseImageInfoList.length" class="mt-8">
      <div class="flex gap-8 items-center justify-end mb-8">
        <el-button @click="responseImageInfoList = []">清空</el-button>
        <el-button type="primary" @click="copy">复制</el-button>
        <el-button type="success" @click="previewCsv">CSV预览</el-button>
      </div>
      <el-table :data="responseImageInfoList">
        <el-table-column label="图片" prop="dataUrl" width="100">
          <template #default="scope">
            <el-image class="h-60 w-60" fit="contain" :src="scope.row.dataUrl" />
          </template>
        </el-table-column>
        <el-table-column label="原名" prop="original_name" width="240" />
        <el-table-column label="图片地址" prop="url" />
      </el-table>
    </div>
    <el-dialog v-model="previewCsvDialogVisible" title="CSV预览" width="80%">
      <div class="flex flex-col gap-4 max-h-780 overflow-y-auto">
        <div v-for="(item, index) in previewCsvContentList" :key="index + item">
          {{ item }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { UploadRequestOptions } from 'element-plus'
import type { ImageInfo } from './hooks/useUploadData'
import { useUploadData } from './hooks/useUploadData'

const { uploadImage } = useUploadData()
const responseImageInfoList = ref<(ImageInfo & { dataUrl: string })[]>([])
const loadingList = ref<true[]>([])
const loading = computed(() => loadingList.value.length > 0)
async function upload(options: UploadRequestOptions) {
  try {
    loadingList.value.push(true)
    // 判断文件类型
    // if (!['image/jpeg', 'image/png', 'image/webp'].includes(options.file.type)) {
    //   ElMessage.error(`错误文件类型:${options.file.type}`)
    //   return
    // }
    const { error_code, data, error_msg } = await uploadImage(options.file)
    if (error_code !== 0) {
      ElMessage.error(error_msg)
      return
    }
    const reader = new FileReader()
    reader.readAsDataURL(options.file)
    reader.onload = function () {
      responseImageInfoList.value.push({ ...data[0], dataUrl: reader.result as string })
    }
  }
  catch (e) {
    ElMessage.error('上传失败')
  }
  finally {
    loadingList.value.pop()
  }
}

function copy() {
  const urls = responseImageInfoList.value.map(item => `${item.original_name},${item.url}`).join('\n')
  const { copy } = useClipboard()
  copy(urls)
  ElMessage.success('复制成功')
}

const previewCsvDialogVisible = ref(false)
const previewCsvContentList = ref<string[]>([])
function previewCsv() {
  const urls = responseImageInfoList.value.map(item => `${item.original_name},${item.url}`)
  previewCsvContentList.value = urls
  previewCsvDialogVisible.value = true
}
</script>

<style lang="scss" scoped>
:deep(.el-upload-dragger) {
  border-width: 3px;
  border-radius: 24px;
  padding: 20px;
}
</style>
