import type { AccountPermissionConfig } from '@/views/account/hooks/types'

export const ADMIN_AUTH_CONFIG: AccountPermissionConfig = {
  product_permissions: {
    menu: {
      home: {
        auth: 1,
      },
      rosetta_x: {
        auth: 1,
      },
      tipping_point: {
        auth: 1,
      },
      market_sensor: {
        auth: 1,
      },
      social_marketing: {
        auth: 1,
      },
      ai_product: {
        auth: 1,
      },
      mobile_app: {
        auth: 1,
      },
      ecommerce: {
        auth: 1,
      },
      industry_automobile: {
        auth: 1,
      },
      research_findings: {
        auth: 1,
      },
      geo: {
        auth: 1,
      },
    },
    download: [
      'ai_product',
    ],
  },
  data_permissions: {
    tipping_point: {
      modules: {
        exploding_word: {
          filters: {
            keywords_category: {
              china: {
                white_list: [],
                black_list: [],
                default: [
                  '1',
                ],
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: [
                  '14',
                ],
              },
            },
          },
        },
      },
    },
    market_sensor: {
      max_limit: {},
      filters: {
        analysis_subjects: {
          black_list: [],
          white_list: [],
          default: [
            {
              filtered_words: '',
              key: 'supermarket',
              keywords: '沃尔玛',
              type: '零售商超',
            },
          ],
        },
      },
      modules: {
        market_psych: {
          filters: {
            brands_industry: {
              black_list: [],
              white_list: [],
              default: [],
            },
          },
        },
        event_detection: {
          filters: {
            event_industry: {
              black_list: [],
              white_list: [],
              default: [],
            },
          },
        },
        market_risk: {
          filters: {
            risk_brand: {
              black_list: [],
              white_list: [],
              default: 1030000000003416,
            },
          },
        },
        live_detection: {
          filters: {
            data_source: {
              china: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'weibo',
                      site_name: '新浪门户_新浪微博',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'weixin',
                      site_name: '微信公众号',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'xiaohongshu',
                      site_name: '小红书',
                      tags: [],
                    },
                    {
                      type: 'shortVideo',
                      source: 'shortVideo',
                      site_name: '抖音app',
                      site_id: '1003583',
                      tags: [],
                    },
                    {
                      type: 'shortVideo',
                      source: 'shortVideo',
                      site_name: '快手app',
                      site_id: '1334510',
                      tags: [],
                    },
                  ],
                },
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Facebook',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Twitter',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Tiktok',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Youtube',
                      tags: [],
                    },
                  ],
                },
              },
            },
          },
        },
        word_explorer: {
          filters: {
            data_source: {
              china: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'xiaohongshu',
                      site_name: '小红书',
                      tags: [],
                    },
                  ],
                },
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Facebook',
                      tags: [],
                    },
                  ],
                },
              },
            },
          },
        },
      },
    },
    social_marketing: {
      filters: {},
      modules: {
        brand_awareness: {
          filters: {
            brand: {
              black_list: [],
              default: '五粮液',
              white_list: [],
            },
            needs_industry: {
              black_list: [],
              default: [
                '23',
                '24',
                '346',
              ],
              white_list: [],
            },
          },
        },
        circle_perspective: {
          filters: {
            attention_industry: {
              black_list: [],
              default: [
                '23',
                '24',
                '346',
              ],
              white_list: [],
            },
            share_brand: {
              black_list: [],
              default: 1030000000003416,
              white_list: [],
            },
          },
        },
        detonation_point: {
          filters: {
            activity_industry: {
              black_list: [],
              default: [
                '23',
              ],
              white_list: [],
            },
          },
        },
        influence: {
          filters: {
            influence_industry: {
              black_list: [],
              default: [
                '23', '24',
              ],
              white_list: [],
            },
          },
        },
        satisfaction_analysis: {
          filters: {
            entity: {
              black_list: [],
              default: 491,
              white_list: [],
            },
          },
        },
      },
    },
    ai_product: {
      menu_time_range: {},
      menu_entity: {},
    },
    mobile_app: {
      menu_time_range: {},
      menu_entity: {},
    },
    rosetta_x: {
      max_limit: {},
    },
    research_findings: {
      max_limit: {},
      filters: {
        analysis_subjects: {
          black_list: [],
          white_list: [],
          default: [
            {
              filtered_words: '',
              key: 'supermarket',
              keywords: '沃尔玛',
              type: '零售商超',
            },
          ],
        },
      },
      modules: {
        word_explorer: {
          filters: {
            data_source: {
              china: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'xiaohongshu',
                      site_name: '小红书',
                      tags: [],
                    },
                  ],
                },
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Facebook',
                      tags: [],
                    },
                  ],
                },
              },
            },
          },
        },
      },
    },
  },
}

export const DEFAULT_AUTH_PERMISSIONS: AccountPermissionConfig = {
  product_permissions: {
    menu: {},
    download: [],
  },
  data_permissions: {
    tipping_point: {
      modules: {
        exploding_word: {
          filters: {
            keywords_category: {
              china: {
                white_list: [],
                black_list: [],
                default: ['1'],
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: ['14'],
              },
            },
          },
        },
      },
    },
    market_sensor: {
      max_limit: {},
      filters: {
        analysis_subjects: {
          black_list: [],
          white_list: [],
          default: [
            {
              filtered_words: '',
              key: 'supermarket',
              keywords: '沃尔玛',
              type: '零售商超',
            },
          ],
        },
      },
      modules: {
        market_psych: {
          filters: {
            brands_industry: {
              black_list: [],
              white_list: [],
              default: [],
            },
          },
        },
        event_detection: {
          filters: {
            event_industry: {
              black_list: [],
              white_list: [],
              default: [],
            },
          },
        },
        market_risk: {
          filters: {
            risk_brand: {
              black_list: [],
              white_list: [],
              default: 1030000000003416,
            },
          },
        },
        live_detection: {
          filters: {
            data_source: {
              china: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'weibo',
                      site_name: '新浪门户_新浪微博',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'weixin',
                      site_name: '微信公众号',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'xiaohongshu',
                      site_name: '小红书',
                      tags: [],
                    },
                    {
                      type: 'shortVideo',
                      source: 'shortVideo',
                      site_name: '抖音app',
                      site_id: '1003583',
                      tags: [],
                    },
                    {
                      type: 'shortVideo',
                      source: 'shortVideo',
                      site_name: '快手app',
                      site_id: '1334510',
                      tags: [],
                    },
                  ],
                },
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Facebook',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Twitter',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Tiktok',
                      tags: [],
                    },
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Youtube',
                      tags: [],
                    },
                  ],
                },
              },
            },
          },
        },
        word_explorer: {
          filters: {
            data_source: {
              china: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'xiaohongshu',
                      site_name: '小红书',
                      tags: [],
                    },
                  ],
                },
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Facebook',
                      tags: [],
                    },
                  ],
                },
              },
            },
          },
        },
      },
    },
    social_marketing: {
      filters: {},
      modules: {
        brand_awareness: {
          filters: {
            brand: {
              black_list: [],
              default: '五粮液',
              white_list: [],
            },
            needs_industry: {
              black_list: [],
              default: [
                '23',
                '24',
                '346',
              ],
              white_list: [],
            },
          },
        },
        circle_perspective: {
          filters: {
            attention_industry: {
              black_list: [],
              default: [
                '23',
                '24',
                '346',
              ],
              white_list: [],
            },
            share_brand: {
              black_list: [],
              default: 1030000000003416,
              white_list: [],
            },
          },
        },
        detonation_point: {
          filters: {
            activity_industry: {
              black_list: [],
              default: [
                '23',
              ],
              white_list: [],
            },
          },
        },
        influence: {
          filters: {
            influence_industry: {
              black_list: [],
              default: [
                '23', '24',
              ],
              white_list: [],
            },
          },
        },
        satisfaction_analysis: {
          filters: {
            entity: {
              black_list: [],
              default: 491,
              white_list: [],
            },
          },
        },
      },
    },
    ai_product: {
      menu_time_range: {},
      menu_entity: {},
    },
    mobile_app: {
      menu_time_range: {},
      menu_entity: {},
    },
    rosetta_x: {
      max_limit: {},
    },
    research_findings: {
      max_limit: {},
      filters: {
        analysis_subjects: {
          black_list: [],
          white_list: [],
          default: [
            {
              filtered_words: '',
              key: 'supermarket',
              keywords: '沃尔玛',
              type: '零售商超',
            },
          ],
        },
      },
      modules: {
        word_explorer: {
          filters: {
            data_source: {
              china: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'xiaohongshu',
                      site_name: '小红书',
                      tags: [],
                    },
                  ],
                },
              },
              overseas: {
                white_list: [],
                black_list: [],
                default: {
                  data_repo: [],
                  site: [
                    {
                      type: 'social',
                      source: 'social',
                      site_name: 'Facebook',
                      tags: [],
                    },
                  ],
                },
              },
            },
          },
        },
      },
    },
  },
}
