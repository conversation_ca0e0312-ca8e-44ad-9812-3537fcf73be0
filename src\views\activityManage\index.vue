<template>
  <div class="flex flex-col gap-16 h-full">
    <div class="flex justify-between">
      <el-segmented v-model="tab" :options="tabOptions" />
      <div v-if="tab === '活动创建/迁移'" class="flex gap-8 items-center">
        <a href="https://xsignal-ai.feishu.cn/wiki/RcsUwx1q8iU22KkqGgYcyOx0nsc" target="_blank">
          文档
        </a>
        <el-button @click="createdActivityListRef?.loadData()">
          <IEpRefresh />
        </el-button>
        <el-button
          :loading="createActivityDisabled || createStatus === 'loading'"
          type="primary"
          @click="onCreateActivity"
        >
          生成活动
        </el-button>
        <el-button @click="onMoveActivityToLib">迁移到stage活动库</el-button>
      </div>
    </div>
    <CreatedActivityList v-if="tab === '活动创建/迁移'" ref="createdActivityListRef" />
    <ManageActivityList v-else-if="tab === '活动管理'" />
  </div>
</template>

<script setup lang="ts">
import CreatedActivityList from './components/CreatedActivityList.vue'
import ManageActivityList from './components/ManageActivityList.vue'
import { useData } from './hooks/useData'
import { useLocalStorage } from '@vueuse/core'

const createdActivityListRef = ref<InstanceType<typeof CreatedActivityList>>()
const { createActivityFromForm, moveActivityListToLib } = useData()

const tab = ref('活动创建/迁移')
const tabOptions = [
  { value: '活动创建/迁移', label: '活动创建/迁移' },
  { value: '活动管理', label: '活动管理' },
]

// 存储上次点击生成活动按钮的时间
const lastCreateActivityTime = useLocalStorage('last-create-activity-time', 0)

// 计算按钮是否应该禁用（10分钟内不能再次点击）
const createActivityDisabled = computed(() => {
  // 如果lastCreateActivityTime为0，说明用户从未点击过按钮，应该允许点击
  if (lastCreateActivityTime.value === 0) return false

  const now = Date.now()
  const tenMinutesInMs = 10 * 60 * 1000
  return now - lastCreateActivityTime.value < tenMinutesInMs
})

const createStatus = ref<DataStatus>('normal')
async function onCreateActivity() {
  createStatus.value = 'loading'

  // 更新最后点击时间
  lastCreateActivityTime.value = Date.now()

  if (!createdActivityListRef.value) return
  const {
    loadData,
  } = createdActivityListRef.value

  const { data, error_code } = await createActivityFromForm()
  if (error_code === 0) {
    ElMessage.success(`${data}, 10分钟内不能再次点击`)
    loadData()
  }
  else {
    ElMessage.error(data)
  }

  createStatus.value = 'normal'
}

async function onMoveActivityToLib() {
  if (!createdActivityListRef.value) return
  const {
    tableData,
    loadData,
  } = createdActivityListRef.value

  const selectedActivityList = tableData.filter((_: any) => _.selected)
  if (selectedActivityList.length === 0) {
    ElMessage.warning('请选择需要迁移的活动')
    return
  }
  if (selectedActivityList.some(_ => _.status !== 'SUCCESS')) {
    ElMessage.warning('请选择状态为成功的活动')
    return
  }
  const activityCodeList = selectedActivityList.map(_ => _.activity_code)
  const { data, error_code } = await moveActivityListToLib(activityCodeList)

  if (error_code === 0) {
    ElMessage.success(data)
    loadData()
  }
  else {
    ElMessage.error(data)
  }
}
</script>

<style scoped>

</style>
