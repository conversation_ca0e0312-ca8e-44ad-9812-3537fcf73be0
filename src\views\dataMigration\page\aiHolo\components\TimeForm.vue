<template>
  <div class="flex justify-center">
    <div class="bg-default flex flex-col gap-16 h-fit p-24 rounded-24 shadow-md w-400">
      <!-- 时间类型选择器 -->
      <el-radio-group
        v-model="dateType"
        :disabled="createMigrationStatus === 'loading'"
        @change="checkLatestMigration"
      >
        <el-radio-button v-if="dateTypeOptions === 'week' || dateTypeOptions === 'both'" :value="1">周</el-radio-button>
        <el-radio-button v-if="dateTypeOptions === 'month' || dateTypeOptions === 'both'" :value="2">月</el-radio-button>
      </el-radio-group>

      <!-- 表单 -->
      <el-form
        v-loading="migrationListStatus === 'loading'"
        class="flex-1"
        :label-position="labelPosition"
        label-width="auto"
      >
        <el-form-item label="选择时间">
          <el-date-picker
            v-model="time"
            class="!w-full"
            :disabled="disabledMigration"
            :placeholder="dateType === 1 ? '请选择周' : '请选择月'"
            :type="dateType === 1 ? 'week' : 'month'"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="最新迁移状态">
          <el-tag
            :type="migrationStatus === 2 ? 'success' : 'primary'"
          >
            {{ migrationStatusMap[migrationStatus] }}
          </el-tag>
        </el-form-item>
        <div class="mt-auto text-end">
          <el-button
            :loading="disabledMigration"
            type="primary"
            @click="onSubmit"
          >
            迁移
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { useData } from '../../hook/aiAndApp/useData'
import { isEmpty } from 'lodash-es'
import { ElMessage } from 'element-plus'

const props = withDefaults(defineProps<{
  entityType?: 'AI' | 'APP' | 'AI_TMT'
  dateTypeOptions?: 'week' | 'month' | 'both'
}>(), {
  entityType: 'AI',
  dateTypeOptions: 'both',
})

const menuMagicCode = computed(() => {
  const codeMap = {
    AI: 1,
    APP: 2,
    AI_TMT: 99,
  } as const
  return codeMap[props.entityType]
})

const {
  createMigration,
  getMigrationLatestRun,
} = useData()

const labelPosition = 'top'

const dateType = ref<1 | 2>(props.dateTypeOptions === 'month' ? 2 : 1)

const time = ref('')

const migrationStatus = ref<keyof typeof migrationStatusMap>(2)
const migrationStatusMap = {
  1: '迁移中',
  2: '迁移成功',
  3: '未知',
  4: '迁移失败',
}
const migrationListStatus = ref<DataStatus>()
const createMigrationStatus = ref<DataStatus>()
const disabledMigration = computed(() => {
  return createMigrationStatus.value === 'loading' || migrationStatus.value === 1
})

function validateForm() {
  if (!time.value) {
    ElMessage.warning('请选择时间')
    return false
  }
  return true
}

async function checkLatestMigration() {
  migrationListStatus.value = 'loading'

  const { data, error_msg } = await getMigrationLatestRun({
    menu: menuMagicCode.value,
    migration_period: dateType.value,
    type: 'time',
  })

  if (!isEmpty(data)) {
    const statusMap = {
      SUCCESS: 2,
      FAILURE: 4,
      CANCELED: 4,
    } as const
    migrationStatus.value = statusMap[data.status as keyof typeof statusMap] || 1
  }
  else {
    migrationStatus.value = 3
    ElMessage.error(error_msg)
  }

  if (data?.status !== 'SUCCESS') {
    time.value = ''
  }

  migrationListStatus.value = 'normal'
}

async function onSubmit() {
  // 校验表单
  if (!validateForm()) {
    return
  }

  createMigrationStatus.value = 'loading'

  const { error_code } = await createMigration({
    menu: menuMagicCode.value,
    migration_period: dateType.value,
    migration_date: dayjs(time.value)
      .startOf(dateType.value === 1 ? 'week' : 'month')
      .format('YYYY-MM-DD'),
  })

  if (error_code === 0) {
    ElMessage.success('提交成功')
  }
  else {
    ElMessage.error('提交失败')
  }

  checkLatestMigration()

  createMigrationStatus.value = 'normal'
}

onMounted(() => {
  checkLatestMigration()
})
</script>

<style scoped>

</style>
