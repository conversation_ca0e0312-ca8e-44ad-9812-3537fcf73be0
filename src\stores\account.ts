import type { LoginUserInfo } from '@/apis/auth'
import { getToken, removeToken, setToken } from '@/utils/cookies'
import type { AccountPermissionConfig } from '@/views/account/hooks/types'
import { logout, userInfo as userInfoApi } from '@/apis/auth'
import { useAccountData } from '@/views/account/hooks/useAccountData'
import { transformToCascaderOptions } from '@/utils/transform'

interface IndustryOptions {
  mp_industry_code: string
  industry_name: string
  industry_en: string
  industry_level: number
  industry_parent: string
  enabled: number
  id: number
  children?: IndustryOptions[]
}
export interface SMIndustryOptions {
  'sort': number
  'industry_code': string
  'industry_name': string
  'industry_parent': string
  'industry_level': number
  'enabled': number
  children?: SMIndustryOptions[]
}

interface IndustryList {
  industry_code: number
  industry_name: string
}

export const useAccountStore = defineStore('account', () => {
  const industryOptions = ref<IndustryOptions[]>([])
  const smIndustryOptions = ref<SMIndustryOptions[]>([])
  const smEntityIndustryOptions = ref<OptionItem<number>[]>([])
  const userInfo = ref<LoginUserInfo>()
  const token = ref<string>(getToken() || '')
  const userPermission = ref<AccountPermissionConfig>()

  function updateUserInfo(data: LoginUserInfo) {
    userInfo.value = data
    token.value = data.token
    setToken(data.token)
  }

  const { getUserAuthConfig } = useAccountData()
  async function getUserInfo() {
    if (!userInfo.value) {
      const { data, code } = await userInfoApi()
      if (code === 20000 && data) {
        userInfo.value = {
          token: token.value,
          account: data.account,
          user_code: data.user_code,
          user_name: data.user_name,
        }
      }
    }
    if (!userInfo.value) return
    const { error_code, data } = await getUserAuthConfig(userInfo.value.user_code)
    if (error_code === 0 && data) {
      userPermission.value = data
    }
  }

  const logoutAndClear = async () => {
    await logout()
    token.value = ''
    userInfo.value = undefined
    userPermission.value = undefined
    removeToken()
    location.reload()
  }

  function hasMenuPermission(menu: string) {
    const configPermission = userPermission.value?.config_permissions
    if (!configPermission) {
      // 未初始化该权限，默认全都有
      return true
    }
    try {
      const menuPermission = configPermission.menu
      return menuPermission[menu] === 1
    }
    catch (error) {
      console.error(error)
      return true
    }
  }

  async function getSmIndustry() {
    const queryParams = {
      query: {
        methodName: 'buzz_industry',
        order: {
          sort: 'asc',
        },
        filters: [],
        limit: 2000,
      },
    }
    const { data } = await loadV2(queryParams)
    const options = transformToCascaderOptions(data, {
      labelKey: 'industry_name',
      valueKey: 'industry_code',
      parentKey: 'industry_parent',
    })
    return options
  }

  async function getSmEntityIndustry() {
    const { data } = await talonApi<IndustryList[]>({
      methodName: 'buzz_industry_info_list',
    })
    const options = data.map(item => ({
      value: item.industry_code,
      label: item.industry_name,
    })).sort((a, b) => {
      return a.label.localeCompare(b.label)
    })
    return options
  }

  return {
    industryOptions,
    smIndustryOptions,
    smEntityIndustryOptions,
    userInfo,
    token,
    updateUserInfo,
    getUserInfo,
    userPermission,
    hasMenuPermission,
    logoutAndClear,
    getSmIndustry,
    getSmEntityIndustry,
  }
})
