<template>
  <div class="flex flex-col gap-16 h-full">
    <Handsontable
      ref="hotTableRef"
      :columns="columns"
      :data="tableData"
      :fixed-columns-left="3"
      :post-transform="postTransform"
      :transform="transform"
    />
    <el-button class="self-end" type="primary" @click="onConfirm">
      保存
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { TaskStepCheckStatusData } from '../hooks/type'
import { useData } from '../hooks/useData'
import { useFilter } from '../hooks/useFilter'

const emit = defineEmits<{
  (e: 'saveSuccess'): void
}>()

const {
  getAiWebTaskStepCheckStatusData,
  saveAiWebTaskStepCheckStatusData,
} = useData()
const {
  taskId,
  entityType,
} = useFilter()

const hotTableRef = shallowRef()
const matchingStatusOptions = [
  { label: '已存在', value: 1 },
  { label: '待新增', value: 2 },
  { label: '待再次核验量级', value: 4 },
  { label: '有近似产品', value: 3 },
]

const columns = computed(() => {
  const fixColumns = [
    {
      name: 'auto_matching_status',
      label: '自动匹配状态',
      editor: false,
    },
    {
      name: 'final_status',
      label: '人工确认状态',
      editor: 'select',
      selectOptions: matchingStatusOptions.slice(0, 3).map(_ => _.label),
    },

  ]
  if (entityType.value === 'ai_web') {
    return [
      {
        name: 'name',
        label: 'WEB名称',
        editor: false,
      },
      {
        name: 'domain',
        label: '网址',
        editor: false,
      },
      {
        name: 'web_name',
        label: '相似实体名',
        editor: false,
      },
      {
        name: 'is_collect',
        label: '是否收录',
        editor: false,
      },
      {
        name: 'scale',
        label: '量级',
        editor: false,
      },
      {
        name: 'is_scale_standard',
        label: '量级是否达标',
        editor: false,
      },
      ...fixColumns,
    ]
  }
  else {
    return [
      {
        name: 'name',
        label: 'APP名称',
      },
      {
        name: 'region',
        label: '地区',
      },
      ...fixColumns,
    ]
  }
})

function transform(list: any[]) {
  return list.map((data) => {
    const res = {
      ...data,
      auto_matching_status: matchingStatusOptions.find(_ => _.value === data.auto_matching_status)?.label,
      is_collect: data.is_collect ? '是' : '否',
      is_scale_standard: data.is_scale_standard ? '是' : '否',
    }
    return res
  })
}

function postTransform(list: any[]) {
  return list.map((data) => {
    const res = {
      ...data,
      auto_matching_status: matchingStatusOptions.find(_ => _.label === data.auto_matching_status)?.value,
      final_status: matchingStatusOptions.find(_ => _.label === data.final_status)?.value,
    }
    return res
  })
}

const tableData = ref<TaskStepCheckStatusData[]>([])

async function loadData() {
  const { data } = await getAiWebTaskStepCheckStatusData(taskId.value)
  tableData.value = data
}

loadData()

async function onConfirm() {
  const changedData: TaskStepCheckStatusData[] = hotTableRef.value?.getData()
  const errCondition = changedData.some((data: TaskStepCheckStatusData) => !data.final_status)
  if (errCondition) {
    ElMessage.error('请确认所有记录的人工确认状态')
    return
  }
  const { error_code, error_msg } = await saveAiWebTaskStepCheckStatusData(
    taskId.value,
    changedData,
  )
  if (error_code === 0) {
    ElMessage.success('保存成功')
    emit('saveSuccess')
  }
  else {
    ElMessage.error(error_msg)
  }
}
</script>

<style scoped lang="scss">

</style>
