<template>
  <div class="flex flex-col h-full">
    <h1>已保存的实体列表</h1>
    <Handsontable
      ref="webInfoHotTableRef"
      v-loading="webInfoStatus === 'loading'"
      class="flex-1"
      :columns="webInfoColumns"
      :data="webInfoList"
      :fixed-columns-left="3"
      :post-transform="webInfoPostTransform"
      :transform="webInfoTransform"
    />
    <h1>已保存的映射表</h1>
    <Handsontable
      ref="matchHotTableRef"
      v-loading="matchStatus === 'loading'"
      class="flex-1"
      :columns="matchColumns"
      :data="matchList"
      :post-transform="matchPostTransform"
    />
    <el-button
      class="self-end"
      type="primary"
      @click="onConfirm"
    >
      保存
    </el-button>
  </div>
</template>

<script setup lang="ts">
import type { HandsontableColumn } from '@/components/Handsontable/Handsontable.vue'
import type { TaskStepMatchData, WebInfo } from '../hooks/type'
import { useData } from '../hooks/useData'
import { useFilter } from '../hooks/useFilter'
import { ElMessage } from 'element-plus'

const emit = defineEmits<{
  (e: 'saveSuccess'): void
}>()

const {
  getAiWebInfo,
  getAiWebTaskStepMatchData,
  saveAiWebTaskStepFinalCheckData,
} = useData()
const {
  taskId,
  allAiIndustryList,
  allAiList,
} = useFilter()

const webInfoHotTableRef = ref()
const webInfoList = ref<WebInfo[]>([])
const webInfoStatus = ref<DataStatus>('normal')
const collectStatusOptions = [
  {
    label: '正常周期采集',
    value: 0,
  },
  {
    label: '停止采集',
    value: 1,
  },
  {
    label: '单独需求采集',
    value: 2,
  },
]
const webInfoColumns: HandsontableColumn[] = [
  {
    name: 'web_id',
    label: 'WEB ID',
    editor: false,
  },
  {
    name: 'name',
    label: 'WEB名称',
  },
  {
    name: 'logo_url',
    label: '图标URL',
    renderer(instance, TD, row, col, prop, value) {
      const html = `<img src="${value}" style="width: 56px; height: 56px;">`
      TD.innerHTML = html
    },
  },
  {
    name: 'domain',
    label: '网址',
  },
  {
    name: 'is_collect',
    label: '是否收录（人工）',
    editor: 'select',
    selectOptions: ['是', '否'],
  },
  {
    name: 'company_fname',
    label: '公司全称',
  },
  {
    name: 'company_sname',
    label: '公司简称',
  },
  {
    name: 'group_company',
    label: '所属集团',
  },
  {
    name: 'region',
    label: '地区',
    editor: 'select',
    selectOptions: ['中国', '海外'],
  },
  {
    name: 'is_export',
    label: '是否出海',
    editor: 'select',
    selectOptions: ['是', '否'],
  },
  {
    name: 'industry_l1_code_ai_label',
    label: 'AI行业打标结果',
    editor: false,
    renderer(instance, TD, row, col, prop, value) {
      if (!value) {
        return
      }
      const codeList = value.split(',')
      const codeListStr = codeList.map((code: string) => {
        const codeItem = allAiIndustryList.value.find(item => item.industry_code === Number(code))
        return codeItem?.industry_name
      }).join('<br/>')
      TD.innerHTML = codeListStr
    },
  },
  {
    name: 'is_ai',
    label: 'AI原生',
    editor: 'select',
    selectOptions: ['是', '否'],
  },
  {
    name: 'industry_l1_code',
    label: '一级行业 ID',
    type: 'autocomplete',
    strict: true,
    source: allAiIndustryList.value.map(item => item.industry_name),
  },
  {
    name: 'release_time',
    label: '上线时间',
    type: 'date',
    dateFormat: 'YYYY-MM',
    correctFormat: true,
    allowEmpty: false,
  },
  {
    name: 'collect_status',
    label: '采集状态',
    editor: false,
  },
  {
    name: 'introduction',
    label: '描述',
  },
  {
    name: 'remark',
    label: '备注',
  },
  {
    name: 'scale',
    label: '量级',
    editor: false,
  },
  {
    name: 'is_scale_standard',
    label: '量级是否达标',
    editor: false,
  },
  {
    name: 'discover_time',
    label: '首次新增时间',
    editor: false,
  },
  {
    name: 'collect_time',
    label: '首次收录时间',
    editor: false,
  },
]
function webInfoTransform(list: WebInfo[]) {
  return list.map((data) => {
    const res: any = {
      ...data,
    }
    res.is_collect = data.is_collect ? '是' : '否'
    res.is_ai = data.is_ai ? '是' : '否'
    res.is_export = data.is_export ? '是' : '否'
    res.is_scale_standard = data.is_scale_standard ? '是' : '否'
    res.region = data.region === 'china' ? '中国' : '海外'
    res.industry_l1_code = allAiIndustryList.value
      .find(item => item.industry_code === data.industry_l1_code)?.industry_name
    res.collect_status = collectStatusOptions.find(item => item.value === data.collect_status)?.label
    return res
  })
}
function webInfoPostTransform(list: WebInfo[]) {
  return list.map((data: any) => {
    const res: any = {
      ...data,
    }
    res.is_collect = data.is_collect === '是'
    res.is_ai = data.is_ai === '是'
    res.is_export = data.is_export === '是'
    res.is_scale_standard = data.is_scale_standard === '是'
    res.region = data.region === '中国' ? 'china' : 'overseas'
    res.industry_l1_code = allAiIndustryList.value
      .find(item => item.industry_name === data.industry_l1_code)?.industry_code
    res.collect_status = collectStatusOptions.find(item => item.label === data.collect_status)?.value
    return res
  })
}
async function loadWebInfoData() {
  webInfoStatus.value = 'loading'
  const { data } = await getAiWebInfo(taskId.value)
  webInfoList.value = data
  webInfoStatus.value = 'normal'
}

const matchHotTableRef = ref()
const matchList = ref<TaskStepMatchData[]>([])
const matchStatus = ref<DataStatus>('normal')
const matchColumns: HandsontableColumn[] = [
  {
    name: 'web_id',
    label: 'WEB ID',
    editor: false,
  },
  {
    name: 'web_name',
    label: 'WEB名称',
    editor: false,
  },
  {
    name: 'app_name',
    label: 'APP名称',
    type: 'autocomplete',
    source: allAiList.value.map(_ => _.name),
  },
]
function matchPostTransform(list: TaskStepMatchData[]) {
  return list.map((data) => {
    const res: any = {
      ...data,
    }
    res.app_id = allAiList.value.find(_ => _.name === data.app_name)?.app_id
    return res
  })
}

async function loadMatchData() {
  matchStatus.value = 'loading'
  const { data } = await getAiWebTaskStepMatchData(taskId.value)
  matchList.value = data
  matchStatus.value = 'normal'
}

function loadData() {
  loadWebInfoData()
  loadMatchData()
}
loadData()

async function onConfirm() {
  const webInfoData = webInfoHotTableRef.value?.getData()
  const matchData = matchHotTableRef.value?.getData()
  const { error_code, error_msg } = await saveAiWebTaskStepFinalCheckData(taskId.value, webInfoData, matchData)
  if (error_code === 0) {
    ElMessage.success('保存成功')
    emit('saveSuccess')
  }
  else {
    ElMessage.error(error_msg)
  }
}
</script>

<style>

</style>
