import { request } from '@/utils/request'
import type { AxiosRequestConfig } from 'axios'

export type TalonApiRequest = {
  methodName: string
  [key: string]: any
} | FormData

export interface TalonApiResponse<T = any> {
  error_code: number
  error_msg: string
  data: T
  total?: number
}

export function talonApi<T = any>(data: TalonApiRequest, config?: AxiosRequestConfig): Promise<TalonApiResponse<T>> {
  return request({
    url: 'eagle/v1/talon',
    method: 'post',
    data,
    ...(config || {}),
  })
}
