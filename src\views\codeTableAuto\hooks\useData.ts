import type { JobRunInfo } from './type'

export function useData() {
  // 根据 dagster job name 获取该 job 最近一次 run 的信息
  async function getJobRunInfoByJobName(jobName: string) {
    const res = await talonApi<JobRunInfo>({
      methodName: 'codetable_task_lastest_run',
      job_name: jobName,
    })
    return res
  }

  // 触发 dagster job 运行
  async function triggerJobRun(jobName: string) {
    const res = await talonApi<{ run_id: string }>({
      methodName: 'codetable_task_create',
      job_name: jobName,
    })
    return res
  }

  return {
    getJobRunInfoByJobName,
    triggerJobRun,
  }
}
