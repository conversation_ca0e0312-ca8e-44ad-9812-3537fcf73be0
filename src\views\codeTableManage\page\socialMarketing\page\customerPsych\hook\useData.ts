export interface CpPdCodeTable {
  created_at: string
  created_by: null
  filter_words: null
  hierarchy_needs: string
  id: number
  industry_code: number
  industry_level: number
  industry_name: string
  industry_parent: number
  keywords: string
  updated_at: string
  updated_by: number
  word: string
}

export function useData() {
  async function getCodeTableList(industry_code: string) {
    const res = await talon<PERSON>pi<CpPdCodeTable[]>({
      methodName: 'sm_up_codetable_list',
      industry_code: Number(industry_code),
    })
    return res
  }

  async function updateCodeTable(data: CpPdCodeTable[]) {
    const res = await talonApi<{ rows: number }>({
      methodName: 'sm_up_codetable_update',
      data,
    })
    return res
  }

  async function addByXlsx(file: File) {
    const formData = new FormData()
    formData.append('methodName', 'sm_up_codetable_batch_create')
    formData.append('file', file)
    const res = await talon<PERSON><PERSON>(formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    return res
  }

  return {
    getCodeTableList,
    updateCodeTable,
    addByXlsx,
  }
}
