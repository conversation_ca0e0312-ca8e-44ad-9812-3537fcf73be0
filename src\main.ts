import App from '@/App.vue'
import { router } from '@/routers/index'
import { i18n } from '@/i18n'

import 'element-plus/theme-chalk/src/form.scss'
import 'element-plus/theme-chalk/el-popover.css'
import 'element-plus/theme-chalk/el-popconfirm.css'
import 'element-plus/theme-chalk/el-message.css'
import '@/styles/index.scss'

// unocss
import 'virtual:uno.css'

// dayjs
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

const pinia = createPinia()
createApp(App)
  .use(i18n)
  .use(pinia)
  .use(router)
  .mount('#app')
