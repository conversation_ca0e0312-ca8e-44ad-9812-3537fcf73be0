<template>
  <el-dialog
    destroy-on-close
    :model-value="visible"
    :title="title"
    width="600px"
    @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="p-16">
      <el-form label-position="left" label-width="100px" :model="form">
        <el-form-item v-if="type === 'edit'" label="行业编码">
          <div>{{ form.industry_code }}</div>
        </el-form-item>
        <el-form-item label="行业名称">
          <el-input v-model="form.industry_name" />
        </el-form-item>
        <el-form-item v-if="level === 2" label="所属行业">
          <el-select v-model="form.industry_parent">
            <el-option v-for="item in industryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="form.enabled" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="form.sort" :max="100" :min="0" :step="1" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex gap-16 justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { PromptIndustry } from '../hooks/useData'

const props = defineProps<{
  type: 'add' | 'edit'
  level: number
  visible: boolean
  data?: Partial<PromptIndustry>
  industryList: PromptIndustry[]
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', formData: Partial<PromptIndustry>): void
}>()

const title = computed(() => {
  const opType = props.type === 'add' ? '新增' : '编辑'
  const level = props.level === 1 ? '一级' : '二级'
  return `${opType}${level}行业`
})

const form = ref<Partial<PromptIndustry>>({
  industry_code: 0,
  industry_name: '',
  industry_level: 0,
  industry_parent: 0,
  enabled: true,
  sort: 0,
})

const industryOptions = computed(() => {
  return props.industryList.filter(item => item.industry_level === 1)
    .map((item) => {
      return {
        label: item.industry_name,
        value: item.industry_code,
      }
    })
})

// 监听 activityData 变化，更新表单数据
watch(() => props.visible, () => {
  if (props.visible) {
    if (props.type === 'add') {
      form.value = cloneDeep({
        industry_name: '',
        industry_level: props.level,
        industry_parent: props.level === 1 ? 0 : props.data?.industry_parent,
        enabled: true,
        sort: props.data?.sort ? props.data.sort + 1 : 0,
      })
    }
    else {
      form.value = cloneDeep({
        ...props.data,
      })
    }
  }
}, { immediate: true, deep: true })

function handleClose() {
  emit('update:visible', false)
}

function handleConfirm() {
  emit('confirm', cloneDeep({
    ...form.value,
  }))
}
</script>

<style scoped>
.p-16 {
  padding: 16px;
}
.border-b {
  border-bottom: 1px solid #ebeef5;
}
</style>
